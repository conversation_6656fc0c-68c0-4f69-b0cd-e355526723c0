//! LUX Launcher - Rust + egui implementation
//! Clean vertical layout with session persistence and key validation

#![windows_subsystem = "windows"]

mod assets;
mod embedded_dll;
mod injector;
mod keyauth;
mod protection;

use eframe::egui::{self, FontId, RichText, Rounding, Stroke, Vec2, Rect};
use parking_lot::Mutex;
use std::path::PathBuf;
use std::sync::atomic::{AtomicBool, AtomicU32, Ordering};
use std::sync::Arc;
use std::thread;
use std::time::{Duration, Instant};
use std::fs;
use std::io::{Read, Write};
use serde::{Deserialize, Serialize};

static INIT_COMPLETE: AtomicBool = AtomicBool::new(false);
static APP_GENERATION: AtomicU32 = AtomicU32::new(0);

// KeyAuth Configuration - credentials are encrypted in keyauth module
const FINGERPRINT_FILE: &str = ".lux_session";
const LOGIN_HISTORY_FILE: &str = ".lux_history";

// Colors matching sensical_cs2 style
#[allow(dead_code)]
mod colors {
    use eframe::egui::Color32;

    pub const ACCENT: Color32 = Color32::from_rgb(48, 194, 255);
    pub const BG_MAIN: Color32 = Color32::from_rgb(12, 12, 14);
    pub const BG_CHILD: Color32 = Color32::from_rgb(22, 24, 26);
    pub const BG_ELEMENT: Color32 = Color32::from_rgb(32, 34, 38);
    pub const BG_BUTTON_ACTIVE: Color32 = Color32::from_rgb(35, 38, 42);
    pub const BG_BUTTON_INACTIVE: Color32 = Color32::from_rgb(28, 30, 34);
    pub const TEXT_ACTIVE: Color32 = Color32::from_rgb(255, 255, 255);
    pub const TEXT_HOVER: Color32 = Color32::from_rgb(90, 90, 90);
    pub const TEXT_DIM: Color32 = Color32::from_rgb(75, 75, 75);
    pub const SUCCESS: Color32 = Color32::from_rgb(0, 204, 102);
    pub const ERROR: Color32 = Color32::from_rgb(255, 77, 77);
    pub const ROUNDING: f32 = 6.0;
}

#[derive(Clone, Copy, PartialEq, Eq)]
enum AppState {
    Splash,
    Initializing,
    Login,
    Authenticated,
    Injecting,
    Done,
    Error,
}

#[derive(Clone, Serialize, Deserialize, Default)]
struct SavedSession {
    username: String,
    license_key: String,
    injection_delay: u32,
}

impl SavedSession {
    fn load() -> Option<Self> {
        let exe_dir = std::env::current_exe().ok()?.parent()?.to_path_buf();
        let path = exe_dir.join(FINGERPRINT_FILE);

        if !path.exists() {
            return None;
        }

        let mut file = fs::File::open(&path).ok()?;
        let mut contents = String::new();
        file.read_to_string(&mut contents).ok()?;

        // Simple base64-like obfuscation
        let decoded = Self::decode(&contents)?;
        serde_json::from_str(&decoded).ok()
    }

    fn save(&self) -> Option<()> {
        let exe_dir = std::env::current_exe().ok()?.parent()?.to_path_buf();
        let path = exe_dir.join(FINGERPRINT_FILE);

        let json = serde_json::to_string(self).ok()?;
        let encoded = Self::encode(&json);

        let mut file = fs::File::create(&path).ok()?;
        file.write_all(encoded.as_bytes()).ok()?;
        Some(())
    }

    fn delete() -> Option<()> {
        let exe_dir = std::env::current_exe().ok()?.parent()?.to_path_buf();
        let path = exe_dir.join(FINGERPRINT_FILE);

        if path.exists() {
            fs::remove_file(&path).ok()?;
        }
        Some(())
    }

    fn encode(data: &str) -> String {
        use base64::Engine;
        let hwid = keyauth::KeyAuthApi::get_hwid();
        let key = hwid.as_bytes();
        let encrypted: Vec<u8> = data.bytes()
            .enumerate()
            .map(|(i, b)| b ^ key[i % key.len()])
            .collect();
        base64::engine::general_purpose::STANDARD.encode(encrypted)
    }

    fn decode(data: &str) -> Option<String> {
        use base64::Engine;
        let hwid = keyauth::KeyAuthApi::get_hwid();
        let key = hwid.as_bytes();
        let encrypted = base64::engine::general_purpose::STANDARD.decode(data).ok()?;
        let decrypted: Vec<u8> = encrypted.iter()
            .enumerate()
            .map(|(i, b)| b ^ key[i % key.len()])
            .collect();
        String::from_utf8(decrypted).ok()
    }
}

#[derive(Clone, Serialize, Deserialize, Default)]
struct LoginHistory {
    entries: Vec<LoginEntry>,
    hwid: String,
}

#[derive(Clone, Serialize, Deserialize)]
struct LoginEntry {
    username: String,
    license_key: String,
}

impl LoginHistory {
    fn load() -> Self {
        let exe_dir = match std::env::current_exe().ok().and_then(|p| p.parent().map(|p| p.to_path_buf())) {
            Some(d) => d,
            None => return Self::default(),
        };
        let path = exe_dir.join(LOGIN_HISTORY_FILE);
        if !path.exists() {
            return Self::default();
        }
        let contents = fs::read_to_string(&path).unwrap_or_default();
        let decoded = SavedSession::decode(&contents).unwrap_or_default();
        let history: Self = serde_json::from_str(&decoded).unwrap_or_default();

        let current_hwid = keyauth::KeyAuthApi::get_hwid();
        if history.hwid != current_hwid {
            return Self::default();
        }
        history
    }

    fn save(&self) {
        let exe_dir = match std::env::current_exe().ok().and_then(|p| p.parent().map(|p| p.to_path_buf())) {
            Some(d) => d,
            None => return,
        };
        let path = exe_dir.join(LOGIN_HISTORY_FILE);
        if let Ok(json) = serde_json::to_string(self) {
            let encoded = SavedSession::encode(&json);
            let _ = fs::write(&path, encoded);
        }
    }

    fn add(&mut self, username: &str, license_key: &str) {
        self.hwid = keyauth::KeyAuthApi::get_hwid();
        self.entries.retain(|e| e.username != username);
        self.entries.insert(0, LoginEntry {
            username: username.to_string(),
            license_key: license_key.to_string(),
        });
        if self.entries.len() > 5 {
            self.entries.truncate(5);
        }
        self.save();
    }

    fn remove(&mut self, username: &str) {
        self.entries.retain(|e| e.username != username);
        self.save();
    }
}

struct AppData {
    state: AppState,
    status_message: String,
    error_message: String,
    warning_message: String,
    username: String,
    license_key: String,
    user_data: Option<keyauth::UserData>,
    injection_progress: f32,
    injection_delay: u32,
    hwid: String,
    splash_start: std::time::Instant,
}

impl Default for AppData {
    fn default() -> Self {
        let saved = SavedSession::load().unwrap_or_default();
        let delay = if saved.injection_delay > 0 { saved.injection_delay } else { injector::DEFAULT_INJECT_DELAY };

        Self {
            state: AppState::Splash,
            status_message: "".to_string(),
            error_message: String::new(),
            warning_message: String::new(),
            username: saved.username,
            license_key: saved.license_key,
            user_data: None,
            injection_progress: 0.0,
            injection_delay: delay,
            hwid: keyauth::KeyAuthApi::get_hwid(),
            splash_start: std::time::Instant::now(),
        }
    }
}

struct LuxLauncher {
    data: Arc<Mutex<AppData>>,
    auth: Arc<Mutex<Option<keyauth::KeyAuthApi>>>,
    is_processing: Arc<AtomicBool>,
    dll_path: Option<PathBuf>,
    logo_texture: Option<egui::TextureHandle>,
    login_history: LoginHistory,
    session_valid: Arc<AtomicBool>,
    last_session_check: Arc<Mutex<Instant>>,
    shutdown_flag: Arc<AtomicBool>,
    should_exit: Arc<AtomicBool>,
    generation: u32,
}

const SESSION_CHECK_INTERVAL_SECS: u64 = 45; // Check session every 45 seconds

impl LuxLauncher {
    fn new(cc: &eframe::CreationContext<'_>) -> Self {
        Self::configure_style(&cc.egui_ctx);
        let logo_texture = Self::load_logo_texture(&cc.egui_ctx);
        let generation = APP_GENERATION.fetch_add(1, Ordering::SeqCst);
        let app = Self {
            data: Arc::new(Mutex::new(AppData::default())),
            auth: Arc::new(Mutex::new(None)),
            is_processing: Arc::new(AtomicBool::new(false)),
            dll_path: Self::find_dll(),
            logo_texture,
            login_history: LoginHistory::load(),
            session_valid: Arc::new(AtomicBool::new(true)),
            last_session_check: Arc::new(Mutex::new(Instant::now())),
            shutdown_flag: Arc::new(AtomicBool::new(false)),
            should_exit: Arc::new(AtomicBool::new(false)),
            generation,
        };
        INIT_COMPLETE.store(true, Ordering::SeqCst);
        app
    }



    /// Check session validity (called from UI thread for immediate feedback)
    fn check_session_now(&self) -> bool {
        if !self.session_valid.load(Ordering::SeqCst) {
            return false;
        }

        let auth_guard = self.auth.lock();
        if let Some(ref api) = *auth_guard {
            if let Ok(valid) = api.check_session() {
                if !valid {
                    self.session_valid.store(false, Ordering::SeqCst);
                    return false;
                }
            }
        }
        true
    }

    fn load_logo_texture(ctx: &egui::Context) -> Option<egui::TextureHandle> {
        let image = image::load_from_memory(assets::LUX_LOGO_PNG).ok()?;
        let rgba = image.to_rgba8();
        let size = [rgba.width() as usize, rgba.height() as usize];
        let pixels = rgba.into_raw();
        let color_image = egui::ColorImage::from_rgba_unmultiplied(size, &pixels);
        Some(ctx.load_texture("lux_logo", color_image, egui::TextureOptions::LINEAR))
    }

    fn find_dll() -> Option<PathBuf> {
        embedded_dll::find_dll()
    }

    fn configure_style(ctx: &egui::Context) {
        let mut style = (*ctx.style()).clone();

        style.visuals.dark_mode = true;
        style.visuals.panel_fill = colors::BG_MAIN;
        style.visuals.window_fill = colors::BG_MAIN;
        style.visuals.extreme_bg_color = colors::BG_ELEMENT;

        style.visuals.widgets.noninteractive.bg_fill = colors::BG_CHILD;
        style.visuals.widgets.noninteractive.fg_stroke = Stroke::new(1.0, colors::TEXT_DIM);
        style.visuals.widgets.noninteractive.rounding = Rounding::same(colors::ROUNDING);

        style.visuals.widgets.inactive.bg_fill = colors::BG_ELEMENT;
        style.visuals.widgets.inactive.fg_stroke = Stroke::new(1.0, colors::TEXT_ACTIVE);
        style.visuals.widgets.inactive.rounding = Rounding::same(colors::ROUNDING);

        style.visuals.widgets.hovered.bg_fill = colors::BG_BUTTON_ACTIVE;
        style.visuals.widgets.hovered.fg_stroke = Stroke::new(1.0, colors::TEXT_ACTIVE);
        style.visuals.widgets.hovered.rounding = Rounding::same(colors::ROUNDING);

        style.visuals.widgets.active.bg_fill = colors::ACCENT;
        style.visuals.widgets.active.fg_stroke = Stroke::new(1.0, colors::BG_MAIN);
        style.visuals.widgets.active.rounding = Rounding::same(colors::ROUNDING);

        style.visuals.selection.bg_fill = colors::ACCENT.linear_multiply(0.3);
        style.visuals.selection.stroke = Stroke::new(1.0, colors::ACCENT);

        style.spacing.item_spacing = Vec2::new(8.0, 8.0);
        style.spacing.button_padding = Vec2::new(12.0, 8.0);

        ctx.set_style(style);
    }

    fn initialize_keyauth(&self) {
        let data = Arc::clone(&self.data);
        let auth = Arc::clone(&self.auth);

        thread::spawn(move || {
            let mut api = match keyauth::KeyAuthApi::new_protected() {
                Ok(api) => api,
                Err(e) => {
                    let mut data = data.lock();
                    data.state = AppState::Error;
                    data.error_message = format!("Failed to connect: {}", e);
                    return;
                }
            };

            match api.init() {
                Ok(()) => {
                    let mut data = data.lock();
                    data.state = AppState::Login;
                    data.status_message = "Ready to authenticate".to_string();
                    drop(data);
                    *auth.lock() = Some(api);

                    // Start session validator after successful init
                    // Note: We'll start it after first successful auth instead
                }
                Err(e) => {
                    let mut data = data.lock();
                    data.state = AppState::Error;
                    data.error_message = format!("Failed to connect: {}", e);
                }
            }
        });
    }

    fn process_authentication(&self) {
        if self.is_processing.load(Ordering::SeqCst) {
            return;
        }

        let data = Arc::clone(&self.data);
        let auth = Arc::clone(&self.auth);
        let is_processing = Arc::clone(&self.is_processing);
        let session_valid = Arc::clone(&self.session_valid);
        let last_session_check = Arc::clone(&self.last_session_check);
        let shutdown_flag = Arc::clone(&self.shutdown_flag);
        let current_gen = self.generation;

        let username = data.lock().username.clone();
        let license_key = data.lock().license_key.clone();
        let delay = data.lock().injection_delay;

        if license_key.is_empty() {
            data.lock().error_message = "Please enter a license key".to_string();
            return;
        }

        is_processing.store(true, Ordering::SeqCst);

        thread::spawn(move || {
            {
                let mut d = data.lock();
                d.status_message = "Authenticating...".to_string();
                d.error_message.clear();
            }

            let mut auth_guard = auth.lock();
            let api = match auth_guard.as_mut() {
                Some(api) => api,
                None => {
                    data.lock().error_message = "Not initialized".to_string();
                    is_processing.store(false, Ordering::SeqCst);
                    return;
                }
            };

            let result = api.login(&license_key, &license_key)
                .or_else(|_| api.register_silent(&license_key, &license_key, &license_key));

            match result {
                Ok(()) => {
                    // Mark session as valid
                    session_valid.store(true, Ordering::SeqCst);

                    // Start session validator in background
                    let auth_clone = Arc::clone(&auth);
                    let data_clone = Arc::clone(&data);
                    let session_valid_clone = Arc::clone(&session_valid);
                    let last_check_clone = Arc::clone(&last_session_check);
                    let shutdown_clone = shutdown_flag.clone();
                    let validator_gen = current_gen;
                    thread::spawn(move || {
                        loop {
                            for _ in 0..SESSION_CHECK_INTERVAL_SECS {
                                thread::sleep(Duration::from_secs(1));
                                if shutdown_clone.load(Ordering::SeqCst) {
                                    return;
                                }
                                if APP_GENERATION.load(Ordering::SeqCst) != validator_gen {
                                    return;
                                }
                            }

                            let current_state = data_clone.lock().state;
                            if current_state != AppState::Authenticated {
                                break;
                            }

                            let session_result = {
                                let auth_guard = auth_clone.lock();
                                if let Some(ref api) = *auth_guard {
                                    api.check_session().ok()
                                } else {
                                    None
                                }
                            };

                            if let Some(valid) = session_result {
                                if !valid {
                                    session_valid_clone.store(false, Ordering::SeqCst);
                                    let mut d = data_clone.lock();
                                    d.state = AppState::Login;
                                    d.error_message = "Session invalidated - key may be in use elsewhere".to_string();
                                    d.user_data = None;
                                    break;
                                }
                            }

                            *last_check_clone.lock() = Instant::now();
                        }
                    });

                    // Run security validation
                    if let Err(error_msg) = api.validate_security() {
                        let mut d = data.lock();
                        d.state = AppState::Login;
                        d.error_message = error_msg;
                        is_processing.store(false, Ordering::SeqCst);
                        return;
                    }

                    let session = SavedSession {
                        username: username.clone(),
                        license_key: license_key.clone(),
                        injection_delay: delay,
                    };
                    let _ = session.save();

                    let mut history = LoginHistory::load();
                    history.add(&username, &license_key);

                    let mut d = data.lock();
                    d.state = AppState::Authenticated;
                    // Use input username, not API response (API may return license key)
                    let display_name = if api.user.username.is_empty() || api.user.username.contains("-") {
                        username.clone()
                    } else {
                        api.user.username.clone()
                    };
                    d.status_message = format!("Welcome, {}!", display_name);
                    // Store the display name in user_data
                    let mut user_data = api.user.clone();
                    user_data.username = display_name;
                    d.user_data = Some(user_data);

                    // Warn if expiring soon (within 24 hours)
                    if api.expires_within_hours(24) {d.warning_message = format!("Warning: Key expires in {} hours!", api.hours_remaining());
                    } else if api.expires_within_hours(72) {
                        d.warning_message = format!("Key expires in {} days", api.days_remaining());
                    }
                }
                Err(e) => {
                    let mut d = data.lock();
                    d.error_message = keyauth::format_auth_error(&e.to_string());
                    d.status_message = "Authentication failed".to_string();
                    let failed_user = d.username.clone();
                    drop(d);

                    let mut history = LoginHistory::load();
                    history.remove(&failed_user);
                }
            }

            is_processing.store(false, Ordering::SeqCst);
        });
    }

    fn process_logout(&mut self) {
        if let Some(ref auth) = *self.auth.lock() {
            auth.log_logout();
        }
        self.shutdown_flag.store(true, Ordering::SeqCst);
        self.session_valid.store(false, Ordering::SeqCst);
        *self.auth.lock() = None;
        SavedSession::delete();
        {
            let mut data = self.data.lock();
            data.state = AppState::Initializing;
            data.user_data = None;
            data.username.clear();
            data.license_key.clear();
            data.error_message.clear();
            data.warning_message.clear();
            data.status_message = "Re-initializing...".to_string();
        }
        self.login_history = LoginHistory::load();
        thread::sleep(Duration::from_millis(100));
        self.shutdown_flag.store(false, Ordering::SeqCst);
        self.initialize_keyauth();
    }

    /// Check key validity using KeyAuth API security checks
    fn check_key_validity(&mut self) -> bool {
        let auth_guard = self.auth.lock();

        if let Some(api) = auth_guard.as_ref() {
            // Run full security validation
            if let Err(error_msg) = api.validate_security() {
                drop(auth_guard);
                self.process_logout();
                self.data.lock().error_message = error_msg;
                return false;
            }

            // Warn if expiring soon (within 24 hours)
            if api.expires_within_hours(24) {
                let hours = api.hours_remaining();
                drop(auth_guard);
                self.data.lock().warning_message = format!("Warning: Key expires in {} hours!", hours);
            }

            true
        } else {
            false
        }
    }

    fn process_injection(&mut self) {
        if self.is_processing.load(Ordering::SeqCst) {
            return;
        }

        // Check session is still valid before injection
        if !self.check_session_now() {
            self.data.lock().error_message = "Session invalid - please re-login".to_string();
            self.data.lock().state = AppState::Login;
            return;
        }

        // Check key validity before injection
        if !self.check_key_validity() {
            return;
        }

        // Get user data with corrected username (from AppData, not raw auth)
        let user_data_for_injection = self.data.lock().user_data.clone();

        // Verify user data exists - REQUIRED for authentication
        let user = match user_data_for_injection {
            Some(ref u) => u.clone(),
            None => {
                self.data.lock().error_message = "No user data available - please re-authenticate".to_string();
                self.data.lock().state = AppState::Login;
                return;
            }
        };

        let dll_path = match &self.dll_path {
            Some(p) => p.clone(),
            None => {
                self.data.lock().error_message = "DLL not found".to_string();
                return;
            }
        };

        let delay = self.data.lock().injection_delay;
        let data = Arc::clone(&self.data);
        let is_processing = Arc::clone(&self.is_processing);
        let should_exit = Arc::clone(&self.should_exit);

        is_processing.store(true, Ordering::SeqCst);

        {
            let mut d = data.lock();
            d.state = AppState::Injecting;
            d.injection_progress = 0.0;
            d.error_message.clear();
        }

        let user_for_thread = user.clone();
        let user_for_injection = user.clone();
        thread::spawn(move || {
            let data_clone = Arc::clone(&data);

            // Pass user_data to perform_injection - session file written RIGHT BEFORE inject_dll
            let result = injector::perform_injection(
                &dll_path,
                delay,
                false,
                true,
                Some(&user_for_injection),
                move |progress: f32, message: &str| {
                    let mut d = data_clone.lock();
                    d.injection_progress = progress;
                    d.status_message = message.to_string();
                },
            );

            match result {
                Ok(()) => {
                    // Log successful injection to Discord
                    if let Some(logger) = keyauth::DiscordLogger::new() {
                        logger.log(keyauth::LogEvent::Injection, &user_for_thread, Some("Injection successful"));
                    }

                    {
                        let mut d = data.lock();
                        d.state = AppState::Done;
                        d.status_message = "Injection successful! Closing in 2s...".to_string();
                    }
                    // Auto-close after 2 seconds on successful injection
                    thread::sleep(Duration::from_secs(2));
                    should_exit.store(true, Ordering::SeqCst);
                }
                Err(e) => {
                    let mut d = data.lock();
                    d.state = AppState::Error;
                    d.error_message = format!("Injection failed: {}", e);
                }
            }

            is_processing.store(false, Ordering::SeqCst);
        });
    }

    fn render_header(&self, ui: &mut egui::Ui) {
        ui.horizontal(|ui| {
            // Logo
            if let Some(texture) = &self.logo_texture {
                ui.image((texture.id(), Vec2::new(28.0, 28.0)));
            }

            ui.add_space(4.0);
            ui.label(RichText::new("LUX").size(22.0).color(colors::ACCENT).strong());});
    }

    fn render_footer(&self, ui: &mut egui::Ui) {
        ui.horizontal(|ui| {
            let data = self.data.lock();
            let hwid_short = &data.hwid[..16.min(data.hwid.len())];
            ui.label(RichText::new(format!("HWID: {}", hwid_short)).size(10.0).color(colors::TEXT_DIM));

            ui.with_layout(egui::Layout::right_to_left(egui::Align::Center), |ui| {
                ui.label(RichText::new("v1.0").size(10.0).color(colors::TEXT_DIM));
            });
        });
    }



    fn render_content(&mut self, ui: &mut egui::Ui) {
        let state = self.data.lock().state;

        match state {
            AppState::Splash => {}, // Splash is rendered as overlay
            AppState::Initializing => self.render_initializing(ui),
            AppState::Login => self.render_login(ui),
            AppState::Authenticated => self.render_authenticated(ui),
            AppState::Injecting => self.render_injecting(ui),
            AppState::Done => self.render_done(ui),
            AppState::Error => self.render_error(ui),
        }
    }

    fn render_splash_overlay(&mut self, ctx: &egui::Context) {
        let elapsed = self.data.lock().splash_start.elapsed();
        let elapsed_secs = elapsed.as_secs_f32();

        // Calculate alpha for fade effect
        let alpha = if elapsed_secs < 1.5 {
            1.0 // Full opacity during display
        } else if elapsed_secs < 2.0 {
            1.0 - ((elapsed_secs - 1.5) * 2.0) // Fade out
        } else {
            0.0
        };

        if alpha <= 0.0 {
            // Transition to initializing
            let mut data = self.data.lock();
            if data.state == AppState::Splash {
                data.state = AppState::Initializing;
                drop(data);
                self.initialize_keyauth();
            }return;
        }

        // Full screen overlay
        let screen_rect = ctx.screen_rect();

        egui::Area::new(egui::Id::new("splash_overlay"))
            .fixed_pos(screen_rect.min)
            .order(egui::Order::Foreground)
            .show(ctx, |ui| {
                let bg_color = egui::Color32::from_rgba_unmultiplied(12, 12, 14, (alpha * 255.0) as u8);

                ui.painter().rect_filled(screen_rect, 0.0, bg_color);

                ui.allocate_new_ui(egui::UiBuilder::new().max_rect(screen_rect), |ui| {
                    ui.vertical_centered(|ui| {
                        let center_y = screen_rect.height() / 2.0 - 60.0;
                        ui.add_space(center_y);

                        // Logo
                        if let Some(texture) = &self.logo_texture {
                            let logo_alpha = (elapsed_secs * 3.0).min(1.0) * alpha;
                            let tint = egui::Color32::from_rgba_unmultiplied(255, 255, 255, (logo_alpha * 255.0) as u8);
                            ui.add(egui::Image::new((texture.id(), Vec2::new(72.0, 72.0))).tint(tint));
                        }

                        ui.add_space(16.0);

                        // LUX text
                        let text_alpha = ((elapsed_secs - 0.3).max(0.0) * 3.0).min(1.0) * alpha;
                        let text_color = egui::Color32::from_rgba_unmultiplied(48, 194, 255, (text_alpha * 255.0) as u8);
                        ui.label(RichText::new("LUX").size(36.0).color(text_color).strong());

                        ui.add_space(4.0);

                        let sub_alpha = ((elapsed_secs - 0.5).max(0.0) * 3.0).min(1.0) * alpha;
                        let sub_color = egui::Color32::from_rgba_unmultiplied(75, 75, 75, (sub_alpha * 255.0) as u8);
                        ui.label(RichText::new("CS2 Loader").size(13.0).color(sub_color));

                        ui.add_space(30.0);

                        // Loading spinner
                        if elapsed_secs > 0.8 && alpha > 0.5 {
                            ui.spinner();
                        }
                    });
                });
            });
    }

    fn render_initializing(&self, ui: &mut egui::Ui) {
        ui.vertical_centered(|ui| {
            ui.add_space(60.0);
            ui.spinner();
            ui.add_space(16.0);
            ui.label(RichText::new("Connecting to server...").color(colors::TEXT_DIM));
        });
    }

    fn render_login(&mut self, ui: &mut egui::Ui) {
        let mut data = self.data.lock();
        let history_entries = self.login_history.entries.clone();
        let mut selected_entry: Option<LoginEntry> = None;

        ui.vertical_centered(|ui| {
            ui.add_space(10.0);
            ui.set_max_width(320.0);

            if !history_entries.is_empty() {
                ui.horizontal(|ui| {
                    ui.label(RichText::new("Previous Logins").size(12.0).color(colors::TEXT_HOVER));
                });
                ui.add_space(2.0);
                egui::ComboBox::from_id_salt("login_history")
                    .width(320.0)
                    .selected_text("Select account...")
                    .show_ui(ui, |ui| {
                        for entry in &history_entries {
                            if ui.selectable_label(false, &entry.username).clicked() {
                                selected_entry = Some(entry.clone());
                            }
                        }
                    });
                ui.add_space(10.0);
            }

            ui.horizontal(|ui| {
                ui.label(RichText::new("Username").size(12.0).color(colors::TEXT_HOVER));
            });
            ui.add_space(2.0);
            ui.add(
                egui::TextEdit::singleline(&mut data.username)
                    .desired_width(320.0)
                    .margin(Vec2::new(8.0, 8.0))
            );

            ui.add_space(10.0);

            ui.horizontal(|ui| {
                ui.label(RichText::new("License Key").size(12.0).color(colors::TEXT_HOVER));
            });
            ui.add_space(2.0);
            ui.add(
                egui::TextEdit::singleline(&mut data.license_key)
                    .desired_width(320.0)
                    .margin(Vec2::new(8.0, 8.0))
            );

            ui.add_space(16.0);

            let is_processing = self.is_processing.load(Ordering::SeqCst);
            let error_msg = data.error_message.clone();
            drop(data);

            let button_text = if is_processing { "Authenticating..." } else { "Login" };
            let button = egui::Button::new(RichText::new(button_text).size(14.0).color(colors::BG_MAIN))
                .fill(if is_processing { colors::BG_BUTTON_ACTIVE } else { colors::ACCENT })
                .min_size(Vec2::new(320.0, 38.0))
                .rounding(Rounding::same(colors::ROUNDING));

            if ui.add_enabled(!is_processing, button).clicked() {
                self.process_authentication();
            }

            if !error_msg.is_empty() {
                ui.add_space(10.0);
                ui.label(RichText::new(&error_msg).color(colors::ERROR).size(11.0));
            }
        });

        if let Some(entry) = selected_entry {
            let mut data = self.data.lock();
            data.username = entry.username;
            data.license_key = entry.license_key;
            drop(data);
            self.process_authentication();
        }
    }

    fn render_authenticated(&mut self, ui: &mut egui::Ui) {
        let data = self.data.lock();
        let user = data.user_data.clone();
        let error_msg = data.error_message.clone();
        drop(data);

        ui.vertical_centered(|ui| {
            ui.add_space(10.0);
            ui.set_max_width(320.0);

            // Welcome message
            if let Some(user) = &user {
                ui.label(RichText::new(format!("Welcome, {}", user.username)).size(16.0).color(colors::ACCENT).strong());

                // Show warning if key expiring soon
                let warning = self.data.lock().warning_message.clone();
                if !warning.is_empty() {
                    ui.add_space(4.0);
                    ui.label(RichText::new(&warning).size(11.0).color(colors::ERROR));
                }

                ui.add_space(12.0);

                // User info card
                egui::Frame::none()
                    .fill(colors::BG_CHILD)
                    .rounding(Rounding::same(colors::ROUNDING))
                    .inner_margin(12.0)
                    .show(ui, |ui| {
                        ui.set_min_width(296.0);
                        ui.vertical_centered(|ui| {
                            ui.horizontal(|ui| {
                                ui.label(RichText::new("Subscription:").size(12.0).color(colors::TEXT_HOVER));
                                ui.label(RichText::new(&user.subscription).size(12.0).color(colors::ACCENT));
                            });

                            ui.add_space(2.0);

                            ui.horizontal(|ui| {
                                ui.label(RichText::new("Expires:").size(12.0).color(colors::TEXT_HOVER));
                                ui.label(RichText::new(format_expiry(user.timeleft)).size(12.0).color(colors::SUCCESS));
                            });

                            ui.add_space(2.0);

                            ui.horizontal(|ui| {
                                ui.label(RichText::new("License:").size(12.0).color(colors::TEXT_HOVER));
                                let key = &self.data.lock().license_key;
                                let masked = if key.len() > 8 {
                                    format!("{}...{}", &key[..4], &key[key.len()-4..])
                                } else {
                                    key.clone()
                                };
                                ui.label(RichText::new(masked).size(12.0).color(colors::TEXT_DIM));
                            });
                        });
                    });
            }

            ui.add_space(12.0);

            // Injection delay slider
            egui::Frame::none()
                .fill(colors::BG_CHILD)
                .rounding(Rounding::same(colors::ROUNDING))
                .inner_margin(10.0)
                .show(ui, |ui| {
                    ui.set_width(296.0);
                    ui.vertical_centered(|ui| {
                        let mut delay = self.data.lock().injection_delay;
                        ui.horizontal(|ui| {
                            ui.label(RichText::new("Injection Delay:").size(12.0).color(colors::TEXT_HOVER));
                            ui.label(RichText::new(format!("{}s", delay)).size(12.0).color(colors::ACCENT));
                        });
                        ui.add_space(4.0);
                        if ui.add_sized([276.0, 18.0], egui::Slider::new(&mut delay, 0..=60).show_value(false)).changed() {
                            self.data.lock().injection_delay = delay;
                        }
                    });
                });

            ui.add_space(8.0);

            // Disclaimer
            egui::Frame::none()
                .fill(colors::BG_ELEMENT)
                .rounding(Rounding::same(colors::ROUNDING))
                .inner_margin(8.0)
                .show(ui, |ui| {
                    ui.set_min_width(296.0);
                    ui.vertical_centered(|ui| {
                        ui.label(RichText::new("Make sure Steam is open - CS2 will launch automatically").size(11.0).color(colors::TEXT_DIM));
                    });
                });

            ui.add_space(12.0);

            // Inject button
            let is_processing = self.is_processing.load(Ordering::SeqCst);
            let has_dll = self.dll_path.is_some();

            let button_text = if !has_dll {
                "DLL Not Found"
            } else if is_processing {
                "Processing..."
            } else {
                "Inject"
            };

            let button = egui::Button::new(RichText::new(button_text).size(14.0).color(colors::BG_MAIN))
                .fill(if has_dll && !is_processing { colors::ACCENT } else { colors::BG_BUTTON_ACTIVE })
                .min_size(Vec2::new(320.0, 38.0))
                .rounding(Rounding::same(colors::ROUNDING));

            if ui.add_enabled(has_dll && !is_processing, button).clicked() {
                self.process_injection();
            }

            if !has_dll {
                ui.add_space(6.0);
                ui.label(RichText::new("DLL not available").color(colors::ERROR).size(10.0));
            }

            if !error_msg.is_empty() {
                ui.add_space(6.0);
                ui.label(RichText::new(&error_msg).color(colors::ERROR).size(10.0));
            }

            ui.add_space(12.0);

            // Logout button
            let logout_btn = egui::Button::new(RichText::new("Logout").size(12.0).color(colors::TEXT_ACTIVE))
                .fill(colors::BG_ELEMENT)
                .min_size(Vec2::new(100.0, 28.0))
                .rounding(Rounding::same(colors::ROUNDING));

            if ui.add(logout_btn).clicked() {
                self.process_logout();
            }
        });
    }

    fn render_injecting(&self, ui: &mut egui::Ui) {
        let data = self.data.lock();
        let progress = data.injection_progress;
        let status = data.status_message.clone();
        drop(data);

        ui.vertical_centered(|ui| {
            ui.add_space(40.0);
            ui.set_max_width(320.0);

            ui.label(RichText::new(&status).size(14.0).color(colors::TEXT_ACTIVE));

            ui.add_space(16.0);

            // Progress bar
            let bar_width = 300.0;
            let bar_height = 6.0;
            let (rect, _) = ui.allocate_exact_size(Vec2::new(bar_width, bar_height), egui::Sense::hover());

            let painter = ui.painter();
            painter.rect_filled(rect, Rounding::same(3.0), colors::BG_ELEMENT);

            let filled_rect = Rect::from_min_size(
                rect.min,
                Vec2::new(rect.width() * progress, bar_height),
            );
            painter.rect_filled(filled_rect, Rounding::same(3.0), colors::ACCENT);

            ui.add_space(10.0);
            ui.label(RichText::new(format!("{:.0}%", progress * 100.0)).size(12.0).color(colors::TEXT_DIM));
        });
    }

    fn render_done(&self, ui: &mut egui::Ui) {
        ui.vertical_centered(|ui| {
            ui.add_space(30.0);

            // Success icon
            let (rect, _) = ui.allocate_exact_size(Vec2::new(56.0, 56.0), egui::Sense::hover());
            ui.painter().circle_filled(rect.center(), 28.0, colors::SUCCESS);
            ui.painter().text(
                rect.center(),
                egui::Align2::CENTER_CENTER,
                "\u{2713}",
                FontId::proportional(28.0),
                colors::BG_MAIN,
            );

            ui.add_space(12.0);
            ui.label(RichText::new("Injection Successful!").size(16.0).color(colors::SUCCESS).strong());

            ui.add_space(6.0);
            ui.label(RichText::new("You can close this launcher now.").size(12.0).color(colors::TEXT_DIM));

            ui.add_space(20.0);

            let close_btn = egui::Button::new(RichText::new("Close").size(13.0).color(colors::BG_MAIN))
                .fill(colors::ACCENT)
                .min_size(Vec2::new(100.0, 32.0))
                .rounding(Rounding::same(colors::ROUNDING));

            if ui.add(close_btn).clicked() {
                std::process::exit(0);
            }
        });
    }

    fn render_error(&self, ui: &mut egui::Ui) {
        let error_msg = self.data.lock().error_message.clone();

        ui.vertical_centered(|ui| {
            ui.add_space(30.0);

            // Error icon
            let (rect, _) = ui.allocate_exact_size(Vec2::new(56.0, 56.0), egui::Sense::hover());
            ui.painter().circle_filled(rect.center(), 28.0, colors::ERROR);
            ui.painter().text(
                rect.center(),
                egui::Align2::CENTER_CENTER,
                "\u{2717}",
                FontId::proportional(28.0),
                colors::BG_MAIN,
            );

            ui.add_space(12.0);
            ui.label(RichText::new("Error").size(16.0).color(colors::ERROR).strong());

            ui.add_space(6.0);
            ui.label(RichText::new(&error_msg).size(12.0).color(colors::TEXT_ACTIVE));

            ui.add_space(20.0);

            let back_btn = egui::Button::new(RichText::new("Back").size(13.0).color(colors::BG_MAIN))
                .fill(colors::ACCENT)
                .min_size(Vec2::new(100.0, 32.0))
                .rounding(Rounding::same(colors::ROUNDING));

            if ui.add(back_btn).clicked() {
                let mut data = self.data.lock();
                data.state = AppState::Login;
                data.error_message.clear();
            }
        });
    }
}

impl eframe::App for LuxLauncher {
    fn update(&mut self, ctx: &egui::Context, _frame: &mut eframe::Frame) {
        if self.should_exit.load(Ordering::SeqCst) {
            ctx.send_viewport_cmd(egui::ViewportCommand::Close);
            return;
        }

        ctx.request_repaint_after(Duration::from_millis(16)); // 60fps for smooth animation

        // Render splash overlay if in splash state
        if self.data.lock().state == AppState::Splash {
            self.render_splash_overlay(ctx);
        }

        egui::CentralPanel::default()
            .frame(egui::Frame::none().fill(colors::BG_MAIN).inner_margin(14.0))
            .show(ctx, |ui| {
                // Header
                self.render_header(ui);

                ui.add_space(6.0);
                ui.separator();
                ui.add_space(6.0);

                // Content (skip during splash)
                let state = self.data.lock().state;
                if state != AppState::Splash {
                    egui::ScrollArea::vertical().show(ui, |ui| {
                        self.render_content(ui);
                    });
                }

                // Footer at bottom
                ui.with_layout(egui::Layout::bottom_up(egui::Align::LEFT), |ui| {
                    self.render_footer(ui);
                    ui.separator();
                    ui.add_space(2.0);
                });
            });
    }
}

fn format_expiry(timeleft: i64) -> String {
    if timeleft <= 0 {
        return "Expired".to_string();
    }

    let days = timeleft / 86400;
    let hours = (timeleft % 86400) / 3600;

    if days > 0 {
        format!("{} days, {} hours", days, hours)
    } else if hours > 0 {
        format!("{} hours", hours)
    } else {
        let minutes = (timeleft % 3600) / 60;
        format!("{} minutes", minutes)
    }
}

fn is_elevated() -> bool {
    use std::mem;
    use std::ptr;

    #[link(name = "advapi32")]
    extern "system" {
        fn OpenProcessToken(
            ProcessHandle: *mut std::ffi::c_void,
            DesiredAccess: u32,
            TokenHandle: *mut *mut std::ffi::c_void,
        ) -> i32;
        fn GetTokenInformation(
            TokenHandle: *mut std::ffi::c_void,
            TokenInformationClass: u32,
            TokenInformation: *mut std::ffi::c_void,
            TokenInformationLength: u32,
            ReturnLength: *mut u32,
        ) -> i32;
    }

    #[link(name = "kernel32")]
    extern "system" {
        fn GetCurrentProcess() -> *mut std::ffi::c_void;
        fn CloseHandle(hObject: *mut std::ffi::c_void) -> i32;
    }

    const TOKEN_QUERY: u32 = 0x0008;
    const TOKEN_ELEVATION: u32 = 20;

    #[repr(C)]
    struct TokenElevation {
        token_is_elevated: u32,
    }

    unsafe {
        let mut token_handle: *mut std::ffi::c_void = ptr::null_mut();
        if OpenProcessToken(GetCurrentProcess(), TOKEN_QUERY, &mut token_handle) == 0 {
            return false;
        }

        let mut elevation: TokenElevation = mem::zeroed();
        let mut return_length: u32 = 0;
        let result = GetTokenInformation(
            token_handle,
            TOKEN_ELEVATION,
            &mut elevation as *mut _ as *mut std::ffi::c_void,
            mem::size_of::<TokenElevation>() as u32,
            &mut return_length,
        );

        CloseHandle(token_handle);

        result != 0 && elevation.token_is_elevated != 0
    }
}

fn load_icon() -> Option<egui::IconData> {
    let icon_bytes = include_bytes!("assets/icon.png");
    let image = image::load_from_memory(icon_bytes).ok()?.to_rgba8();
    let (width, height) = image.dimensions();
    Some(egui::IconData {
        rgba: image.into_raw(),
        width,
        height,
    })
}

fn main() -> eframe::Result<()> {
    // Require admin privileges
    if !is_elevated() {
        rfd::MessageDialog::new()
            .set_title("LUX Launcher")
            .set_description("This application requires administrator privileges.\nPlease run as administrator.")
            .set_level(rfd::MessageLevel::Error)
            .show();
        std::process::exit(1);
    }

    let protection_result = protection::run_protection_checks();
    if protection_result.should_exit {
        rfd::MessageDialog::new()
            .set_title("LUX Launcher")
            .set_description(&protection_result.message)
            .set_level(rfd::MessageLevel::Error)
            .show();
        std::process::exit(1);
    }

    let icon = load_icon();

    let mut viewport = egui::ViewportBuilder::default()
        .with_inner_size([380.0, 420.0])
        .with_min_inner_size([380.0, 380.0])
        .with_resizable(true)
        .with_decorations(true)
        .with_transparent(false)
        .with_title("LUX Launcher");

    if let Some(icon_data) = icon {
        viewport = viewport.with_icon(std::sync::Arc::new(icon_data));
    }

    let options = eframe::NativeOptions {
        viewport,
        renderer: eframe::Renderer::Glow,
        ..Default::default()
    };

    eframe::run_native(
        "LUX Launcher",
        options,
        Box::new(|cc| Ok(Box::new(LuxLauncher::new(cc)))),
    )
}
