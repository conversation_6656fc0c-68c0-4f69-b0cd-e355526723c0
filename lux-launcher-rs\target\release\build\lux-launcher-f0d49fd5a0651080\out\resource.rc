#pragma code_page(65001)
1 VERSIONINFO
FILESUBTYPE 0x0
PRODUCTVERSION 1, 0, 0, 0
FILETYPE 0x1
FILEOS 0x40004
FILEFLAGSMASK 0x3f
FILEVERSION 1, 0, 0, 0
FILEFLAGS 0x0
{
BLOCK "StringFileInfo"
{
BLOCK "000004b0"
{
VALUE "FileDescription", "LUX CS2 Cheat Loader"
VALUE "LegalCopyright", "LUX"
VALUE "ProductVersion", "1.0.0"
VALUE "ProductName", "LUX Launcher"
VALUE "FileVersion", "1.0.0"
}
}
BLOCK "VarFileInfo" {
VALUE "Translation", 0x0, 0x04b0
}
}
1 ICON "src/assets/icon.ico"
1 24
{
"  "
" <?xml version=""1.0"" encoding=""UTF-8"" standalone=""yes""?> "
" <assembly xmlns=""urn:schemas-microsoft-com:asm.v1"" manifestVersion=""1.0""> "
" <assemblyIdentity version=""*******"" processorArchitecture=""*"" name=""LUX.Launcher"" type=""win32""/> "
" <description>LUX Launcher</description> "
" <trustInfo xmlns=""urn:schemas-microsoft-com:asm.v3""> "
" <security> "
" <requestedPrivileges> "
" <requestedExecutionLevel level=""asInvoker"" uiAccess=""false""/> "
" </requestedPrivileges> "
" </security> "
" </trustInfo> "
" <compatibility xmlns=""urn:schemas-microsoft-com:compatibility.v1""> "
" <application> "
" <supportedOS Id=""{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}""/> "
" <supportedOS Id=""{1f676c76-80e1-4239-95bb-83d0f6d0da78}""/> "
" <supportedOS Id=""{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}""/> "
" <supportedOS Id=""{35138b9a-5d96-4fbd-8e2d-a2440225f93a}""/> "
" <supportedOS Id=""{e2011457-1546-43c5-a5fe-008deee3d3f0}""/> "
" </application> "
" </compatibility> "
" </assembly> "
}

