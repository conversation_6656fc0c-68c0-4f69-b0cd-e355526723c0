#pragma once
#include "../../Precompiled.h"

// CS2 Weapon ID definitions for unified weapon system
#define WEAPON_NONE                       0
#define WEAPON_KNIFE                       1
#define WEAPON_KNIFE_BAYONET              2
#define WEAPON_KNIFE_FLIP                  3
#define WEAPON_KNIFE_GUT                   4
#define WEAPON_KNIFE_KARAMBIT              5
#define WEAPON_KNIFE_M9_BAYONET            6
#define WEAPON_KNIFE_TACTICAL              7
#define WEAPON_KNIFE_FALCHION               8
#define WEAPON_KNIFE_BOWIE                  9
#define WEAPON_KNIFE_BUTTERFLY             10
#define WEAPON_KNIFE_PUSH                  11
#define WEAPON_KNIFE_CORD                   12
#define WEAPON_KNIFE_CANIS                  13
#define WEAPON_KNIFE_URSUS                  14
#define WEAPON_KNIFE_SKELETON               15
#define WEAPON_KNIFE_WIDOWMAKER            16
#define WEAPON_DEAGLE                       17
#define WEAPON_REVOLVER                     18
#define WEAPON_SCAR20                       19
#define WEAPON_NEGEV                        20
#define WEAPON_HKP2000                      21
#define WEAPON_MP5SD                        22
#define WEAPON_UMP45                         23
#define WEAPON_MAC10                         24
#define WEAPON_FAMAS                         25
#define WEAPON_AUG                           26
#define WEAPON_G3SG1                         27
#define WEAPON_AK47                          28
#define WEAPON_AK74BEU                       29
#define WEAPON_GALILAR                       30
#define WEAPON_SG556                         31
#define WEAPON_GALIL                         32
#define WEAPON_M249                          33
#define WEAPON_M4A1                           34
#define WEAPON_M4A1_SILENCER                 35
#define WEAPON_FIVESEVEN                      36
#define WEAPON_P250                          37
#define WEAPON_TEC9                          38
#define WEAPON_CZ75A                         39
#define WEAPON_MP7                            40
#define WEAPON_MP9                            41
#define WEAPON_BIZON                          42
#define WEAPON_MAG7                           43
#define WEAPON_SAWEDOFF                       45
#define WEAPON_TASER                          46
#define WEAPON_FLASHBANG                       47
#define WEAPON_HEGRENADE                      48
#define WEAPON_SMOKEGRENADE                   49
#define WEAPON_MOLOTOV                        50
#define WEAPON_DECOY                          51
#define WEAPON_INCGRENADE                     52
#define WEAPON_TAGRENADE                     53
#define WEAPON_HEALTHSHOT                      54
#define WEAPON_TACTICAL_AWARENESS_GRENADE      55
#define WEAPON_FISTS                          56
#define WEAPON_BREACHCHARGE                   57
#define WEAPON_TABLET                         58
#define WEAPON_MELEE                          59
#define WEAPON_SHIELD                         60
#define WEAPON_C4                             61
#define WEAPON_SSG08                          62
#define WEAPON_AWP                            63
#define WEAPON_NOVA                          67
#define WEAPON_XM1014                         68
#define WEAPON_P90                            80
#define WEAPON_GLOCK                          92
#define WEAPON_USP_SILENCER                   126
#define WEAPON_ELITE                         127
#define WEAPON_ZEUSX27                       116

// Weapon configuration system - ONLY weapon identification
// Accuracy and Damage configs are in separate modules
namespace WeaponConfig {
    
    // Weapon classification enum
    enum EWeaponClass {
        WEAPON_PISTOL_LIGHT = 0,
        WEAPON_PISTOL_HEAVY = 1,
        WEAPON_SMG = 2,
        WEAPON_RIFLE = 3,
        WEAPON_SHOTGUN = 4,
        WEAPON_SNIPER_SCOUT = 5,
        WEAPON_SNIPER_AUTO = 6,
        WEAPON_SNIPER_BOLT = 7,
        WEAPON_LMG = 8,
        WEAPON_OTHER = 9,
        WEAPON_CLASS_MAX = 10
    };

    // Weapon behavior settings - separate from accuracy/damage
    struct WeaponBehavior_t {
        bool bAutoScope = false;
        bool bAutoStop = true;
        bool bAutoRevolver = false;
        bool bAutoKnife = false;
    };

    // Weapon profile - ONLY identification and behavior
    struct WeaponProfile_t {
        const char* szWeaponName = "Unknown";
        int nWeaponClass = WEAPON_OTHER;
        std::vector<int> vecWeaponIds = {};
        WeaponBehavior_t behavior;
    };

    // Global weapon profiles - identification only
    inline WeaponProfile_t g_WeaponProfiles[WEAPON_CLASS_MAX];
    inline int g_iCurrentWeaponClass = WEAPON_OTHER;
    inline int g_iLastWeaponId = -1;

    // Core functions - weapon identification
    void InitializeWeaponProfiles();
    EWeaponClass GetWeaponClass(int nWeaponId);
    WeaponProfile_t& GetCurrentWeaponProfile();
    WeaponProfile_t& GetWeaponProfile(EWeaponClass nClass);
    
    // Weapon change detection
    bool HasWeaponChanged();
    void UpdateCurrentWeapon();
    
    // Weapon info display
    const char* GetCurrentWeaponName();
    const char* GetWeaponClassName(EWeaponClass nClass);
    
    // Behavior accessors
    WeaponBehavior_t& GetCurrentBehavior();
    WeaponBehavior_t& GetBehavior(EWeaponClass nClass);
    void SetBehavior(EWeaponClass nClass, const WeaponBehavior_t& behavior);
    
    // System initialization
    void Initialize();
}

// Weapon ID mappings for classification
struct WeaponIdMap {
    int nWeaponId;
    WeaponConfig::EWeaponClass nWeaponClass;
};

inline WeaponIdMap g_WeaponClassifications[] = {
    // Light pistols
    {WEAPON_HKP2000, WeaponConfig::WEAPON_PISTOL_LIGHT},
    {WEAPON_USP_SILENCER, WeaponConfig::WEAPON_PISTOL_LIGHT},
    {WEAPON_GLOCK, WeaponConfig::WEAPON_PISTOL_LIGHT},
    {WEAPON_P250, WeaponConfig::WEAPON_PISTOL_LIGHT},
    {WEAPON_CZ75A, WeaponConfig::WEAPON_PISTOL_LIGHT},
    {WEAPON_FIVESEVEN, WeaponConfig::WEAPON_PISTOL_LIGHT},
    {WEAPON_TEC9, WeaponConfig::WEAPON_PISTOL_LIGHT},
    {WEAPON_ELITE, WeaponConfig::WEAPON_PISTOL_LIGHT},
    // Heavy pistols
    {WEAPON_DEAGLE, WeaponConfig::WEAPON_PISTOL_HEAVY},
    {WEAPON_REVOLVER, WeaponConfig::WEAPON_PISTOL_HEAVY},
    
    // SMGs
    {WEAPON_MAC10, WeaponConfig::WEAPON_SMG},
    {WEAPON_MP9, WeaponConfig::WEAPON_SMG},
    {WEAPON_MP7, WeaponConfig::WEAPON_SMG},
    {WEAPON_MP5SD, WeaponConfig::WEAPON_SMG},
    {WEAPON_UMP45, WeaponConfig::WEAPON_SMG},
    {WEAPON_P90, WeaponConfig::WEAPON_SMG},
    {WEAPON_BIZON, WeaponConfig::WEAPON_SMG},
    
    // Rifles
    {WEAPON_AK47, WeaponConfig::WEAPON_RIFLE},
    {WEAPON_M4A1, WeaponConfig::WEAPON_RIFLE},
    {WEAPON_M4A1_SILENCER, WeaponConfig::WEAPON_RIFLE},
    {WEAPON_GALILAR, WeaponConfig::WEAPON_RIFLE},
    {WEAPON_FAMAS, WeaponConfig::WEAPON_RIFLE},
    {WEAPON_AUG, WeaponConfig::WEAPON_RIFLE},
    {WEAPON_SG556, WeaponConfig::WEAPON_RIFLE},
    
    // Shotguns
    {WEAPON_NOVA, WeaponConfig::WEAPON_SHOTGUN},
    {WEAPON_XM1014, WeaponConfig::WEAPON_SHOTGUN},
    {WEAPON_SAWEDOFF, WeaponConfig::WEAPON_SHOTGUN},
    {WEAPON_MAG7, WeaponConfig::WEAPON_SHOTGUN},
    // Snipers
    {WEAPON_SSG08, WeaponConfig::WEAPON_SNIPER_SCOUT},
    {WEAPON_SCAR20, WeaponConfig::WEAPON_SNIPER_AUTO},
    {WEAPON_G3SG1, WeaponConfig::WEAPON_SNIPER_AUTO},
    {WEAPON_AWP, WeaponConfig::WEAPON_SNIPER_BOLT},
    
    // LMGs
    {WEAPON_M249, WeaponConfig::WEAPON_LMG},
    {WEAPON_NEGEV, WeaponConfig::WEAPON_LMG},
    
    // Other
    {WEAPON_KNIFE, WeaponConfig::WEAPON_OTHER},
    {WEAPON_KNIFE_BAYONET, WeaponConfig::WEAPON_OTHER},
    {WEAPON_KNIFE_FLIP, WeaponConfig::WEAPON_OTHER},
    {WEAPON_KNIFE_GUT, WeaponConfig::WEAPON_OTHER},
    {WEAPON_KNIFE_KARAMBIT, WeaponConfig::WEAPON_OTHER},
    {WEAPON_KNIFE_M9_BAYONET, WeaponConfig::WEAPON_OTHER},
    {WEAPON_KNIFE_TACTICAL, WeaponConfig::WEAPON_OTHER},
    {WEAPON_KNIFE_FALCHION, WeaponConfig::WEAPON_OTHER},
    {WEAPON_KNIFE_BOWIE, WeaponConfig::WEAPON_OTHER},
    {WEAPON_KNIFE_BUTTERFLY, WeaponConfig::WEAPON_OTHER},
    {WEAPON_KNIFE_PUSH, WeaponConfig::WEAPON_OTHER},
    {WEAPON_KNIFE_CORD, WeaponConfig::WEAPON_OTHER},
    {WEAPON_KNIFE_CANIS, WeaponConfig::WEAPON_OTHER},
    {WEAPON_KNIFE_URSUS, WeaponConfig::WEAPON_OTHER},
    {WEAPON_KNIFE_SKELETON, WeaponConfig::WEAPON_OTHER},
    {WEAPON_KNIFE_WIDOWMAKER, WeaponConfig::WEAPON_OTHER},
    {WEAPON_FLASHBANG, WeaponConfig::WEAPON_OTHER},
    {WEAPON_HEGRENADE, WeaponConfig::WEAPON_OTHER},
    {WEAPON_SMOKEGRENADE, WeaponConfig::WEAPON_OTHER},
    {WEAPON_MOLOTOV, WeaponConfig::WEAPON_OTHER},
    {WEAPON_DECOY, WeaponConfig::WEAPON_OTHER},
    {WEAPON_INCGRENADE, WeaponConfig::WEAPON_OTHER},
    {WEAPON_TAGRENADE, WeaponConfig::WEAPON_OTHER},
    {WEAPON_HEALTHSHOT, WeaponConfig::WEAPON_OTHER},
    {WEAPON_TACTICAL_AWARENESS_GRENADE, WeaponConfig::WEAPON_OTHER},
    {WEAPON_FISTS, WeaponConfig::WEAPON_OTHER},
    {WEAPON_BREACHCHARGE, WeaponConfig::WEAPON_OTHER},
    {WEAPON_TABLET, WeaponConfig::WEAPON_OTHER},
    {WEAPON_ZEUSX27, WeaponConfig::WEAPON_OTHER}
};