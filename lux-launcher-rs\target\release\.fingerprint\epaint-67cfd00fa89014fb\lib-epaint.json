{"rustc": 17273738520181233363, "features": "[\"bytemuck\", \"default_fonts\", \"epaint_default_fonts\", \"log\"]", "declared_features": "[\"bytemuck\", \"cint\", \"color-hex\", \"deadlock_detection\", \"default\", \"default_fonts\", \"document-features\", \"epaint_default_fonts\", \"log\", \"mint\", \"puffin\", \"rayon\", \"serde\", \"unity\"]", "target": 10495837225410426609, "profile": 5597562194884469390, "path": 7853240799761254257, "deps": [[707738805893328333, "emath", false, 5336529054502811731], [966925859616469517, "ahash", false, 9021568239767878663], [5931649091606299019, "nohash_hasher", false, 6793385952217383382], [8146687621941743410, "epaint_default_fonts", false, 12098951807950008204], [10630857666389190470, "log", false, 6351892990446437237], [12459942763388630573, "parking_lot", false, 8792840785780136908], [13755666026417058023, "ab_glyph", false, 14535018225618453414], [14567563978122280782, "bytemuck", false, 15108095810390363509], [16805990319463827332, "ecolor", false, 10482330061859271678]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\epaint-67cfd00fa89014fb\\dep-lib-epaint", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}