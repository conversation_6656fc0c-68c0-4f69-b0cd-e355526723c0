{"rustc": 17273738520181233363, "features": "[\"http1\", \"ring\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "declared_features": "[\"aws-lc-rs\", \"default\", \"fips\", \"http1\", \"http2\", \"log\", \"logging\", \"native-tokio\", \"ring\", \"rustls-native-certs\", \"rustls-platform-verifier\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "target": 12220062926890100908, "profile": 10004052855522644898, "path": 17704464891212930855, "deps": [[64645024058175247, "pki_types", false, 7284726196941260071], [390686634370472506, "webpki_roots", false, 11133475687407129979], [784494742817713399, "tower_service", false, 6110434461788295973], [1991942485830005045, "tokio_rustls", false, 8624519166381028496], [2620434475832828286, "http", false, 18232885740384947572], [4160778395972110362, "hyper", false, 11571531538470580299], [5296164962160813001, "rustls", false, 16718995342861027555], [7720834239451334583, "tokio", false, 11639528105645351678], [8098305783429564872, "hyper_util", false, 11089647151984223138]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\hyper-rustls-6be945cf168fe9c0\\dep-lib-hyper_rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}