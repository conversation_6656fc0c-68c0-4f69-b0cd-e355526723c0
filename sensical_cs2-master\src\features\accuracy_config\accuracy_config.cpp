#include "accuracy_config.h"

namespace AccuracyConfig {

void InitializeAccuracyProfiles() {
    // Light Pistol (class 0)
    g_AccuracyProfiles[0].ragebot = {65, 75, 60.0f};
    g_AccuracyProfiles[0].legitbot = {8, 12, 0, 2, 100, 100, 0};
    
    // Heavy Pistol (class 1)
    g_AccuracyProfiles[1].ragebot = {75, 85, 70.0f};
    g_AccuracyProfiles[1].legitbot = {10, 15, 0, 3, 100, 100, 0};
    
    // SMG (class 2)
    g_AccuracyProfiles[2].ragebot = {55, 65, 50.0f};
    g_AccuracyProfiles[2].legitbot = {12, 18, 0, 4, 100, 100, 0};
    
    // Rifle (class 3)
    g_AccuracyProfiles[3].ragebot = {60, 70, 65.0f};
    g_AccuracyProfiles[3].legitbot = {10, 16, 0, 3, 100, 100, 0};
    
    // Shotgun (class 4)
    g_AccuracyProfiles[4].ragebot = {70, 80, 40.0f};
    g_AccuracyProfiles[4].legitbot = {15, 20, 0, 5, 100, 100, 0};
    
    // Scout (class 5)
    g_AccuracyProfiles[5].ragebot = {80, 90, 80.0f};
    g_AccuracyProfiles[5].legitbot = {5, 8, 0, 2, 100, 100, 0};
    
    // Auto Sniper (class 6)
    g_AccuracyProfiles[6].ragebot = {85, 95, 85.0f};
    g_AccuracyProfiles[6].legitbot = {4, 6, 0, 2, 100, 100, 0};
    
    // AWP (class 7)
    g_AccuracyProfiles[7].ragebot = {90, 100, 90.0f};
    g_AccuracyProfiles[7].legitbot = {3, 5, 0, 1, 100, 100, 0};
    
    // LMG (class 8)
    g_AccuracyProfiles[8].ragebot = {50, 60, 45.0f};
    g_AccuracyProfiles[8].legitbot = {18, 25, 0, 6, 100, 100, 0};
    
    // Other (class 9)
    g_AccuracyProfiles[9].ragebot = {50, 60, 30.0f};
    g_AccuracyProfiles[9].legitbot = {20, 30, 0, 8, 100, 100, 0};
    
    // Initialize global with rifle defaults
    g_GlobalAccuracy.ragebot = {60, 70, 65.0f};
    g_GlobalAccuracy.legitbot = {10, 16, 0, 3, 100, 100, 0};
}

AccuracySettings_t& GetCurrentAccuracy() {
    if (!g_bUsePerWeaponAccuracy)
        return g_GlobalAccuracy;
    if (g_iCurrentWeaponClass < 0 || g_iCurrentWeaponClass >= 10)
        return g_AccuracyProfiles[9];
    return g_AccuracyProfiles[g_iCurrentWeaponClass];
}

AccuracySettings_t& GetAccuracyForWeapon(int nWeaponClass) {
    if (nWeaponClass < 0 || nWeaponClass >= 10)
        return g_AccuracyProfiles[9];
    return g_AccuracyProfiles[nWeaponClass];
}

void SetGlobalAccuracy(const AccuracySettings_t& config) {
    g_GlobalAccuracy = config;
}

void SetWeaponAccuracy(int nWeaponClass, const AccuracySettings_t& config) {
    if (nWeaponClass >= 0 && nWeaponClass < 10)
        g_AccuracyProfiles[nWeaponClass] = config;
}

// Ragebot getters - current weapon
int GetHitchance() {
    return GetCurrentAccuracy().ragebot.nHitchance;
}

int GetHitchanceOverride() {
    return GetCurrentAccuracy().ragebot.nHitchanceOverride;
}

float GetPointScale() {
    return GetCurrentAccuracy().ragebot.nPointScale;
}

// Ragebot getters - by weapon class
int GetHitchance(int nWeaponClass) {
    return GetAccuracyForWeapon(nWeaponClass).ragebot.nHitchance;
}

int GetHitchanceOverride(int nWeaponClass) {
    return GetAccuracyForWeapon(nWeaponClass).ragebot.nHitchanceOverride;
}

float GetPointScale(int nWeaponClass) {
    return GetAccuracyForWeapon(nWeaponClass).ragebot.nPointScale;
}

// Legitbot getters - current weapon
int GetFov() {
    return GetCurrentAccuracy().legitbot.nFov;
}

int GetSmoothness() {
    return GetCurrentAccuracy().legitbot.nSmoothness;
}

int GetSmoothType() {
    return GetCurrentAccuracy().legitbot.nSmoothType;
}

int GetRandomization() {
    return GetCurrentAccuracy().legitbot.nRandomization;
}

int GetRcsX() {
    return GetCurrentAccuracy().legitbot.nRcsX;
}

int GetRcsY() {
    return GetCurrentAccuracy().legitbot.nRcsY;
}

int GetTargetPriority() {
    return GetCurrentAccuracy().legitbot.nTargetPriority;
}

// Legitbot getters - by weapon class
int GetFov(int nWeaponClass) {
    return GetAccuracyForWeapon(nWeaponClass).legitbot.nFov;
}

int GetSmoothness(int nWeaponClass) {
    return GetAccuracyForWeapon(nWeaponClass).legitbot.nSmoothness;
}

int GetSmoothType(int nWeaponClass) {
    return GetAccuracyForWeapon(nWeaponClass).legitbot.nSmoothType;
}

int GetRandomization(int nWeaponClass) {
    return GetAccuracyForWeapon(nWeaponClass).legitbot.nRandomization;
}

int GetRcsX(int nWeaponClass) {
    return GetAccuracyForWeapon(nWeaponClass).legitbot.nRcsX;
}

int GetRcsY(int nWeaponClass) {
    return GetAccuracyForWeapon(nWeaponClass).legitbot.nRcsY;
}

int GetTargetPriority(int nWeaponClass) {
    return GetAccuracyForWeapon(nWeaponClass).legitbot.nTargetPriority;
}

// Ragebot setters
void SetHitchance(int nWeaponClass, int value) {
    if (nWeaponClass >= 0 && nWeaponClass < 10)
        g_AccuracyProfiles[nWeaponClass].ragebot.nHitchance = value;
}

void SetHitchanceOverride(int nWeaponClass, int value) {
    if (nWeaponClass >= 0 && nWeaponClass < 10)
        g_AccuracyProfiles[nWeaponClass].ragebot.nHitchanceOverride = value;
}

void SetPointScale(int nWeaponClass, float value) {
    if (nWeaponClass >= 0 && nWeaponClass < 10)
        g_AccuracyProfiles[nWeaponClass].ragebot.nPointScale = value;
}

// Legitbot setters
void SetFov(int nWeaponClass, int value) {
    if (nWeaponClass >= 0 && nWeaponClass < 10)
        g_AccuracyProfiles[nWeaponClass].legitbot.nFov = value;
}

void SetSmoothness(int nWeaponClass, int value) {
    if (nWeaponClass >= 0 && nWeaponClass < 10)
        g_AccuracyProfiles[nWeaponClass].legitbot.nSmoothness = value;
}

void SetSmoothType(int nWeaponClass, int value) {
    if (nWeaponClass >= 0 && nWeaponClass < 10)
        g_AccuracyProfiles[nWeaponClass].legitbot.nSmoothType = value;
}

void SetRandomization(int nWeaponClass, int value) {
    if (nWeaponClass >= 0 && nWeaponClass < 10)
        g_AccuracyProfiles[nWeaponClass].legitbot.nRandomization = value;
}

void SetRcsX(int nWeaponClass, int value) {
    if (nWeaponClass >= 0 && nWeaponClass < 10)
        g_AccuracyProfiles[nWeaponClass].legitbot.nRcsX = value;
}

void SetRcsY(int nWeaponClass, int value) {
    if (nWeaponClass >= 0 && nWeaponClass < 10)
        g_AccuracyProfiles[nWeaponClass].legitbot.nRcsY = value;
}

void SetTargetPriority(int nWeaponClass, int value) {
    if (nWeaponClass >= 0 && nWeaponClass < 10)
        g_AccuracyProfiles[nWeaponClass].legitbot.nTargetPriority = value;
}

void UpdateWeaponClass(int nWeaponClass) {
    g_iCurrentWeaponClass = nWeaponClass;
}

void Initialize() {
    InitializeAccuracyProfiles();
    g_iCurrentWeaponClass = 9;
}

} // namespace AccuracyConfig