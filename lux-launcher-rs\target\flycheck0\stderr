   1.019863200s  INFO prepare_target{force=false package_id=lux-launcher v1.0.0 (C:\Users\<USER>\Downloads\CS2\lux-launcher-rs) target="lux-launcher"}: cargo::core::compiler::fingerprint: stale: missing "C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\app.manifest"
   1.079264200s  INFO prepare_target{force=false package_id=lux-launcher v1.0.0 (C:\Users\<USER>\Downloads\CS2\lux-launcher-rs) target="lux-launcher"}: cargo::core::compiler::fingerprint: fingerprint dirty for lux-launcher v1.0.0 (C:\Users\<USER>\Downloads\CS2\lux-launcher-rs)/Check { test: false }/TargetInner { name: "lux-launcher", doc: true, ..: with_path("C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\src\\main.rs", Edition2021) }
   1.079328400s  INFO prepare_target{force=false package_id=lux-launcher v1.0.0 (C:\Users\<USER>\Downloads\CS2\lux-launcher-rs) target="lux-launcher"}: cargo::core::compiler::fingerprint:     dirty: FsStatusOutdated(StaleDepFingerprint { name: "build_script_build" })
   1.218987600s  INFO prepare_target{force=false package_id=lux-launcher v1.0.0 (C:\Users\<USER>\Downloads\CS2\lux-launcher-rs) target="build-script-build"}: cargo::core::compiler::fingerprint: fingerprint dirty for lux-launcher v1.0.0 (C:\Users\<USER>\Downloads\CS2\lux-launcher-rs)/RunCustomBuild/TargetInner { ..: custom_build_target("build-script-build", "C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\build.rs", Edition2021) }
   1.219022900s  INFO prepare_target{force=false package_id=lux-launcher v1.0.0 (C:\Users\<USER>\Downloads\CS2\lux-launcher-rs) target="build-script-build"}: cargo::core::compiler::fingerprint:     dirty: FsStatusOutdated(StaleItem(MissingFile("C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\app.manifest")))
   1.281418500s  INFO prepare_target{force=false package_id=lux-launcher v1.0.0 (C:\Users\<USER>\Downloads\CS2\lux-launcher-rs) target="lux-launcher"}: cargo::core::compiler::fingerprint: fingerprint dirty for lux-launcher v1.0.0 (C:\Users\<USER>\Downloads\CS2\lux-launcher-rs)/Check { test: true }/TargetInner { name: "lux-launcher", doc: true, ..: with_path("C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\src\\main.rs", Edition2021) }
   1.281448000s  INFO prepare_target{force=false package_id=lux-launcher v1.0.0 (C:\Users\<USER>\Downloads\CS2\lux-launcher-rs) target="lux-launcher"}: cargo::core::compiler::fingerprint:     dirty: FsStatusOutdated(StaleDepFingerprint { name: "build_script_build" })
   Compiling lux-launcher v1.0.0 (C:\Users\<USER>\Downloads\CS2\lux-launcher-rs)
    Finished `dev` profile [unoptimized + debuginfo] target(s) in 4.79s
