   0.582696300s  INFO prepare_target{force=false package_id=lux-launcher v1.0.0 (C:\Users\<USER>\Downloads\CS2\lux-launcher-rs) target="lux-launcher"}: cargo::core::compiler::fingerprint: stale: missing "C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\app.manifest"
   0.610809400s  INFO prepare_target{force=false package_id=lux-launcher v1.0.0 (C:\Users\<USER>\Downloads\CS2\lux-launcher-rs) target="lux-launcher"}: cargo::core::compiler::fingerprint: fingerprint dirty for lux-launcher v1.0.0 (C:\Users\<USER>\Downloads\CS2\lux-launcher-rs)/Check { test: false }/TargetInner { name: "lux-launcher", doc: true, ..: with_path("C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\src\\main.rs", Edition2021) }
   0.610848900s  INFO prepare_target{force=false package_id=lux-launcher v1.0.0 (C:\Users\<USER>\Downloads\CS2\lux-launcher-rs) target="lux-launcher"}: cargo::core::compiler::fingerprint:     dirty: FsStatusOutdated(StaleDepFingerprint { name: "build_script_build" })
   0.678001800s  INFO prepare_target{force=false package_id=lux-launcher v1.0.0 (C:\Users\<USER>\Downloads\CS2\lux-launcher-rs) target="build-script-build"}: cargo::core::compiler::fingerprint: fingerprint dirty for lux-launcher v1.0.0 (C:\Users\<USER>\Downloads\CS2\lux-launcher-rs)/RunCustomBuild/TargetInner { ..: custom_build_target("build-script-build", "C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\build.rs", Edition2021) }
   0.678026800s  INFO prepare_target{force=false package_id=lux-launcher v1.0.0 (C:\Users\<USER>\Downloads\CS2\lux-launcher-rs) target="build-script-build"}: cargo::core::compiler::fingerprint:     dirty: FsStatusOutdated(StaleItem(MissingFile("C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\app.manifest")))
   0.713551500s  INFO prepare_target{force=false package_id=lux-launcher v1.0.0 (C:\Users\<USER>\Downloads\CS2\lux-launcher-rs) target="lux-launcher"}: cargo::core::compiler::fingerprint: fingerprint dirty for lux-launcher v1.0.0 (C:\Users\<USER>\Downloads\CS2\lux-launcher-rs)/Check { test: true }/TargetInner { name: "lux-launcher", doc: true, ..: with_path("C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\src\\main.rs", Edition2021) }
   0.713576500s  INFO prepare_target{force=false package_id=lux-launcher v1.0.0 (C:\Users\<USER>\Downloads\CS2\lux-launcher-rs) target="lux-launcher"}: cargo::core::compiler::fingerprint:     dirty: FsStatusOutdated(StaleDepFingerprint { name: "build_script_build" })
   Compiling lux-launcher v1.0.0 (C:\Users\<USER>\Downloads\CS2\lux-launcher-rs)
error: could not compile `lux-launcher` (bin "lux-launcher") due to 1 previous error; 3 warnings emitted
warning: build failed, waiting for other jobs to finish...
error: could not compile `lux-launcher` (bin "lux-launcher" test) due to 1 previous error; 3 warnings emitted
