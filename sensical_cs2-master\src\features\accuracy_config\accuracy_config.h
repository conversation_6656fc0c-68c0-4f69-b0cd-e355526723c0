#pragma once
#include "../../Precompiled.h"

namespace AccuracyConfig {// Ragebot accuracy settings
    struct RagebotAccuracy_t {
        int nHitchance = 60;
        int nHitchanceOverride = 70;
        float nPointScale = 65.0f;
    };

    // Legitbot accuracy settings
    struct LegitbotAccuracy_t {
        int nFov = 8;
        int nSmoothness = 12;
        int nSmoothType = 0;
        int nRandomization = 3;
        int nRcsX = 100;
        int nRcsY = 100;
        int nTargetPriority = 0;};

    // Combined accuracy config per weapon class
    struct AccuracySettings_t {
        RagebotAccuracy_t ragebot;
        LegitbotAccuracy_t legitbot;
    };

    // Per-weapon-class accuracy profiles (10 weapon classes)
    inline AccuracySettings_t g_AccuracyProfiles[10];
    
    // Global accuracy override
    inline AccuracySettings_t g_GlobalAccuracy;
    
    // Control flags
    inline bool g_bUsePerWeaponAccuracy = true;
    inline int g_iCurrentWeaponClass = 9;

    // Core functions
    void InitializeAccuracyProfiles();
    AccuracySettings_t& GetCurrentAccuracy();
    AccuracySettings_t& GetAccuracyForWeapon(int nWeaponClass);
    void SetGlobalAccuracy(const AccuracySettings_t& config);
    void SetWeaponAccuracy(int nWeaponClass, const AccuracySettings_t& config);

    // Ragebot getters
    int GetHitchance();
    int GetHitchanceOverride();
    float GetPointScale();
    int GetHitchance(int nWeaponClass);
    int GetHitchanceOverride(int nWeaponClass);
    float GetPointScale(int nWeaponClass);

    // Legitbot getters
    int GetFov();
    int GetSmoothness();
    int GetSmoothType();
    int GetRandomization();
    int GetRcsX();
    int GetRcsY();
    int GetTargetPriority();
    
    // Legitbot getters by weapon class
    int GetFov(int nWeaponClass);
    int GetSmoothness(int nWeaponClass);
    int GetSmoothType(int nWeaponClass);
    int GetRandomization(int nWeaponClass);
    int GetRcsX(int nWeaponClass);
    int GetRcsY(int nWeaponClass);
    int GetTargetPriority(int nWeaponClass);

    // Ragebot setters
    void SetHitchance(int nWeaponClass, int value);
    void SetHitchanceOverride(int nWeaponClass, int value);
    void SetPointScale(int nWeaponClass, float value);

    // Legitbot setters
    void SetFov(int nWeaponClass, int value);
    void SetSmoothness(int nWeaponClass, int value);
    void SetSmoothType(int nWeaponClass, int value);
    void SetRandomization(int nWeaponClass, int value);
    void SetRcsX(int nWeaponClass, int value);
    void SetRcsY(int nWeaponClass, int value);
    void SetTargetPriority(int nWeaponClass, int value);

    // Update current weapon class
    void UpdateWeaponClass(int nWeaponClass);

    // System init
    void Initialize();
}