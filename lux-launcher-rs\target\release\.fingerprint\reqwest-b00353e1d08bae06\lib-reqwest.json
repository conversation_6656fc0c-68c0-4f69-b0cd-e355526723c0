{"rustc": 17273738520181233363, "features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"json\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"system-proxy\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 13564900855740626210, "path": 12447214367351147576, "deps": [[64645024058175247, "rustls_pki_types", false, 7284726196941260071], [390686634370472506, "webpki_roots", false, 11133475687407129979], [784494742817713399, "tower_service", false, 6110434461788295973], [1788832197870803419, "hyper_rustls", false, 13909376834751658490], [1811549171721445101, "futures_channel", false, 10944080822452279558], [1906322745568073236, "pin_project_lite", false, 13639927188980127958], [1991942485830005045, "tokio_rustls", false, 8624519166381028496], [2517136641825875337, "sync_wrapper", false, 7341955867598770655], [2620434475832828286, "http", false, 18232885740384947572], [4160778395972110362, "hyper", false, 11571531538470580299], [5296164962160813001, "rustls", false, 16718995342861027555], [5404511084185685755, "url", false, 12715436957259030825], [5695049318159433696, "tower", false, 10585068319700653200], [6355489020061627772, "bytes", false, 10891763691645010383], [6803352382179706244, "percent_encoding", false, 136285671154078860], [7620660491849607393, "futures_core", false, 7660061510841979109], [7720834239451334583, "tokio", false, 11639528105645351678], [8098305783429564872, "hyper_util", false, 11089647151984223138], [8434721349366383850, "tower_http", false, 2178586839246192006], [10629569228670356391, "futures_util", false, 797287676767826371], [10630857666389190470, "log", false, 6351892990446437237], [12832915883349295919, "serde_json", false, 13809298441081476709], [13077212702700853852, "base64", false, 12404536659175208349], [13548984313718623784, "serde", false, 750374724720301348], [14084095096285906100, "http_body", false, 9026284559170192414], [16542808166767769916, "serde_urlencoded", false, 13474943355680591160], [16900715236047033623, "http_body_util", false, 16066483775934915178]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\reqwest-b00353e1d08bae06\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}