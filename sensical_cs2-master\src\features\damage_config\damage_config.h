#pragma once
#include "../../Precompiled.h"

namespace DamageConfig {

    // Damage configuration - fully independent from weapon config
    struct DamageSettings_t {
        int nMinDamage = 20;
        int nMinDamageOverride = 30;
        int nTargetPriority = 0;
        std::vector<MenuRageHitbox_t> vecHitboxes = {};
    };

    // Per-weapon-class damage profiles
    inline DamageSettings_t g_DamageProfiles[10];
    
    // Global damage override - applies when not using per-weapon
    inline DamageSettings_t g_GlobalDamage;
    
    // Control flags
    inline bool g_bUsePerWeaponDamage = true;
    inline int g_iCurrentWeaponClass = 9; // Default to OTHER

    // Core functions
    void InitializeDamageProfiles();
    DamageSettings_t& GetCurrentDamage();
    DamageSettings_t& GetDamageForWeapon(int nWeaponClass);
    void SetGlobalDamage(const DamageSettings_t& config);
    void SetWeaponDamage(int nWeaponClass, const DamageSettings_t& config);
    // Individual getters
    int GetMinDamage();
    int GetMinDamageOverride();
    int GetTargetPriority();
    std::vector<MenuRageHitbox_t>& GetHitboxes();
    
    // Individual setters
    void SetMinDamage(int nWeaponClass, int value);
    void SetMinDamageOverride(int nWeaponClass, int value);
    void SetTargetPriority(int nWeaponClass, int value);
    void SetHitboxes(int nWeaponClass, const std::vector<MenuRageHitbox_t>& hitboxes);
    
    // Update current weapon class from weapon system
    void UpdateWeaponClass(int nWeaponClass);
    
    // System init
    void Initialize();
}