#pragma once
#include "../weapon_config/weapon_config.h"

class CLegitbot
{
public:
	struct LegitbotPlayer_t
	{
		CCSPlayerController* m_pController;
		C_CSPlayerPawn* m_pPawn;

		QAngle m_aShootAngle;

		int m_iHealth;
		int m_iFov_CenterToAimpos;
		float m_flDistance;
	};

	QAngle m_aLastPunch = { 0, 0, 0 };
	


public:
	std::vector<EHitBoxes> LegitbotHB_To_HB(ELegitbotHitboxes nLegitbotHB);

	QAngle ApplySmoothing(QAngle aCurrentAngle, QAngle aTargetAngle, int iSmoothness, int iSmoothType, int iRandomization);
	void CreateMove();
	void DrawFovCircle();
	void RCS();
	std::vector<LegitbotPlayer_t> GetPlayers();
};
inline const auto g_Legitbot = std::make_unique<CLegitbot>();