#include "../Precompiled.h"
#include "fonts.h"
#include "new_fonts.h"
#include "../../ext/imgui/imgui_settings.h"
#include "../auth/UserData.h"
#include "../features/weapon_config/weapon_config.h"

namespace font {
    ImFont* inter_bold = nullptr;
    ImFont* inter_default = nullptr;
    ImFont* icon = nullptr;
}

namespace pic {
    ID3D11ShaderResourceView* background = nullptr;
    ID3D11ShaderResourceView* aimbot = nullptr;
    ID3D11ShaderResourceView* visuals = nullptr;
    ID3D11ShaderResourceView* skins = nullptr;
    ID3D11ShaderResourceView* settings = nullptr;
    ID3D11ShaderResourceView* combo_widget = nullptr;
    ID3D11ShaderResourceView* input_widget = nullptr;
    ID3D11ShaderResourceView* menu_settings_icon = nullptr;
    ID3D11ShaderResourceView* circle_success = nullptr;
    ID3D11ShaderResourceView* circle_error = nullptr;
    ID3D11ShaderResourceView* lux_logo = nullptr;
    ID3D11ShaderResourceView* rage_general_icon = nullptr;
    ID3D11ShaderResourceView* rage_accuracy_icon = nullptr;
    ID3D11ShaderResourceView* rage_settings_icon = nullptr;
    ID3D11ShaderResourceView* rage_extras_icon = nullptr;
}

static DWORD picker_flags = ImGuiColorEditFlags_NoSidePreview | ImGuiColorEditFlags_AlphaBar | ImGuiColorEditFlags_NoInputs | ImGuiColorEditFlags_AlphaPreview;
static std::string g_DisplayName = "LUX";

// Minimal state
static bool g_ShowAboutPopup = false;
static float g_SaveNotifyTimer = 0.f;
static float g_GlitchTimer = 0.f;

static const char* GetCheatDisplayName() {
    return "LUX";
}
static int currentWeaponType = 0;

// Helper function to get the enable toggle for the current weapon config
bool& GetWeaponEnabledToggle()
{
    switch (currentWeaponType)
    {
    case LIGHT_PISTOL: return Config::b(g_Variables.m_Ragebot.m_LIGHT_PISTOL_bEnabled);
    case DEAGLE: return Config::b(g_Variables.m_Ragebot.m_DEAGLE_bEnabled);
    case REVOLVER: return Config::b(g_Variables.m_Ragebot.m_REVOLVER_bEnabled);
    case SMG: return Config::b(g_Variables.m_Ragebot.m_SMG_bEnabled);
    case LMG: return Config::b(g_Variables.m_Ragebot.m_LMG_bEnabled);
    case AR: return Config::b(g_Variables.m_Ragebot.m_AR_bEnabled);
    case SHOTGUN: return Config::b(g_Variables.m_Ragebot.m_SHOTGUN_bEnabled);
    case SCOUT: return Config::b(g_Variables.m_Ragebot.m_SCOUT_bEnabled);
    case AUTOSNIPER: return Config::b(g_Variables.m_Ragebot.m_AUTOSNIPER_bEnabled);
    case AWP: return Config::b(g_Variables.m_Ragebot.m_AWP_bEnabled);
    default: return Config::b(g_Variables.m_Ragebot.m_LIGHT_PISTOL_bEnabled);
    }
}

// Global hitboxes - independent of weapon config
std::vector<MenuRageHitbox_t>& GetGlobalHitboxes()
{
    return Config::vhb<MenuRageHitbox_t>(g_Variables.m_Ragebot.m_vecGlobalHitboxes);
}

int& GetPointscaleForCurrentWeapon()
{
    EMenuWeaponType weaponType = GetWeaponMenuType(static_cast<EItemDefinitionIndexes>(LocalPlayerData::m_nWeaponDefinitionIndex));

    switch (weaponType)
    {
    case LIGHT_PISTOL: return Config::i(g_Variables.m_Ragebot.m_LIGHT_PISTOL_iMaxPointscale);
    case DEAGLE: return Config::i(g_Variables.m_Ragebot.m_DEAGLE_iMaxPointscale);
    case REVOLVER: return Config::i(g_Variables.m_Ragebot.m_REVOLVER_iMaxPointscale);
    case SMG: return Config::i(g_Variables.m_Ragebot.m_SMG_iMaxPointscale);
    case LMG: return Config::i(g_Variables.m_Ragebot.m_LMG_iMaxPointscale);
    case AR: return Config::i(g_Variables.m_Ragebot.m_AR_iMaxPointscale);
    case SHOTGUN: return Config::i(g_Variables.m_Ragebot.m_SHOTGUN_iMaxPointscale);
    case SCOUT: return Config::i(g_Variables.m_Ragebot.m_SCOUT_iMaxPointscale);
    case AUTOSNIPER: return Config::i(g_Variables.m_Ragebot.m_AUTOSNIPER_iMaxPointscale);
    case AWP: return Config::i(g_Variables.m_Ragebot.m_AWP_iMaxPointscale);
    default: return Config::i(g_Variables.m_Ragebot.m_LIGHT_PISTOL_iMaxPointscale);
    }
}



int& GetHitchanceForCurrentWeapon()
{
    switch (currentWeaponType)
    {
    case LIGHT_PISTOL: return Config::i(g_Variables.m_Ragebot.m_LIGHT_PISTOL_iHitchance);
    case DEAGLE: return Config::i(g_Variables.m_Ragebot.m_DEAGLE_iHitchance);
    case REVOLVER: return Config::i(g_Variables.m_Ragebot.m_REVOLVER_iHitchance);
    case SMG: return Config::i(g_Variables.m_Ragebot.m_SMG_iHitchance);
    case LMG: return Config::i(g_Variables.m_Ragebot.m_LMG_iHitchance);
    case AR: return Config::i(g_Variables.m_Ragebot.m_AR_iHitchance);
    case SHOTGUN: return Config::i(g_Variables.m_Ragebot.m_SHOTGUN_iHitchance);
    case SCOUT: return Config::i(g_Variables.m_Ragebot.m_SCOUT_iHitchance);
    case AUTOSNIPER: return Config::i(g_Variables.m_Ragebot.m_AUTOSNIPER_iHitchance);
    case AWP: return Config::i(g_Variables.m_Ragebot.m_AWP_iHitchance);
    default: return Config::i(g_Variables.m_Ragebot.m_LIGHT_PISTOL_iHitchance);
    }
}

int& GetMinDamageForCurrentWeapon()
{
    switch (currentWeaponType)
    {
    case LIGHT_PISTOL: return Config::i(g_Variables.m_Ragebot.m_LIGHT_PISTOL_iMinDamage);
    case DEAGLE: return Config::i(g_Variables.m_Ragebot.m_DEAGLE_iMinDamage);
    case REVOLVER: return Config::i(g_Variables.m_Ragebot.m_REVOLVER_iMinDamage);
    case SMG: return Config::i(g_Variables.m_Ragebot.m_SMG_iMinDamage);
    case LMG: return Config::i(g_Variables.m_Ragebot.m_LMG_iMinDamage);
    case AR: return Config::i(g_Variables.m_Ragebot.m_AR_iMinDamage);
    case SHOTGUN: return Config::i(g_Variables.m_Ragebot.m_SHOTGUN_iMinDamage);
    case SCOUT: return Config::i(g_Variables.m_Ragebot.m_SCOUT_iMinDamage);
    case AUTOSNIPER: return Config::i(g_Variables.m_Ragebot.m_AUTOSNIPER_iMinDamage);
    case AWP: return Config::i(g_Variables.m_Ragebot.m_AWP_iMinDamage);
    default: return Config::i(g_Variables.m_Ragebot.m_LIGHT_PISTOL_iMinDamage);
    }
}

int& GetMinDamageOverrideForCurrentWeapon()
{
    switch (currentWeaponType)
    {
    case LIGHT_PISTOL: return Config::i(g_Variables.m_Ragebot.m_LIGHT_PISTOL_iMinDamageOverride);
    case DEAGLE: return Config::i(g_Variables.m_Ragebot.m_DEAGLE_iMinDamageOverride);
    case REVOLVER: return Config::i(g_Variables.m_Ragebot.m_REVOLVER_iMinDamageOverride);
    case SMG: return Config::i(g_Variables.m_Ragebot.m_SMG_iMinDamageOverride);
    case LMG: return Config::i(g_Variables.m_Ragebot.m_LMG_iMinDamageOverride);
    case AR: return Config::i(g_Variables.m_Ragebot.m_AR_iMinDamageOverride);
    case SHOTGUN: return Config::i(g_Variables.m_Ragebot.m_SHOTGUN_iMinDamageOverride);
    case SCOUT: return Config::i(g_Variables.m_Ragebot.m_SCOUT_iMinDamageOverride);
    case AUTOSNIPER: return Config::i(g_Variables.m_Ragebot.m_AUTOSNIPER_iMinDamageOverride);
    case AWP: return Config::i(g_Variables.m_Ragebot.m_AWP_iMinDamageOverride);
    default: return Config::i(g_Variables.m_Ragebot.m_LIGHT_PISTOL_iMinDamageOverride);
    }
}







static int& GetHitchanceOverrideForCurrentWeapon()
{
    switch (currentWeaponType)
    {
    case LIGHT_PISTOL: return Config::i(g_Variables.m_Ragebot.m_LIGHT_PISTOL_iHitchanceOverride);
    case DEAGLE: return Config::i(g_Variables.m_Ragebot.m_DEAGLE_iHitchanceOverride);
    case REVOLVER: return Config::i(g_Variables.m_Ragebot.m_REVOLVER_iHitchanceOverride);
    case SMG: return Config::i(g_Variables.m_Ragebot.m_SMG_iHitchanceOverride);
    case LMG: return Config::i(g_Variables.m_Ragebot.m_LMG_iHitchanceOverride);
    case AR: return Config::i(g_Variables.m_Ragebot.m_AR_iHitchanceOverride);
    case SHOTGUN: return Config::i(g_Variables.m_Ragebot.m_SHOTGUN_iHitchanceOverride);
    case SCOUT: return Config::i(g_Variables.m_Ragebot.m_SCOUT_iHitchanceOverride);
    case AUTOSNIPER: return Config::i(g_Variables.m_Ragebot.m_AUTOSNIPER_iHitchanceOverride);
    case AWP: return Config::i(g_Variables.m_Ragebot.m_AWP_iHitchanceOverride);
    default: return Config::i(g_Variables.m_Ragebot.m_LIGHT_PISTOL_iHitchanceOverride);
    }
}


static const char* FormatDamageValue(int value)
{
    static char buffer[32];
    if (value < 0) {
        strcpy_s(buffer, sizeof(buffer), "0HP");
    } else if (value >= 100) {
        strcpy_s(buffer, sizeof(buffer), "100+HP");
    } else {
        sprintf_s(buffer, sizeof(buffer), "%dHP", value);
    }
    return buffer;
}

static const char* FormatPointscale(int value)
{
    static char buffer[32];
    if (value <= 0) {
        strcpy_s(buffer, sizeof(buffer), "Auto");
        return buffer;
    }
    _snprintf_s(buffer, sizeof(buffer), _TRUNCATE, "%d", value);
    return buffer;
}

void Gui::Initialize(unsigned int uFontFlags)
{
    ImGui::StyleColorsDark();

    // Disable anti-aliasing for crisp, sharp rendering
    ImGuiStyle& style = ImGui::GetStyle();
    style.AntiAliasedLines = false;
    style.AntiAliasedLinesUseTex = false;
    style.AntiAliasedFill = false;

    // Layout defaults
    style.WindowPadding = ImVec2(Layout::WindowPadding, Layout::WindowPadding);
    style.FramePadding = ImVec2(Layout::InnerPadding, 2.0f); // Keep vertical frame padding small for compact inputs
    style.ItemSpacing = ImVec2(Layout::ItemSpacingX, Layout::ItemSpacingY);
    style.ItemInnerSpacing = ImVec2(Layout::InnerPadding, Layout::InnerPadding);

    style.WindowRounding = 0.0f;
    style.ChildRounding = 0.0f;
    style.FrameRounding = 0.0f;
    style.ScrollbarRounding = 0.0f;
    style.GrabRounding = 0.0f;
    style.TabRounding = 0.0f;

    ImGuiIO& io = ImGui::GetIO();
    io.FontGlobalScale = 1.4f;
    io.FontAllowUserScaling = false;

    // Use clean font config with crisp rendering
    ImFontConfig cfg;
    cfg.FontBuilderFlags = ImGuiFreeTypeBuilderFlags_ForceAutoHint;
    cfg.OversampleH = 3;
    cfg.OversampleV = 1;
    cfg.PixelSnapH = false;

    // Load clean, readable fonts
    // Removed redundant font:: assignments

    // FontAwesome glyph range (U+e005 to U+f8ff)
    static const ImWchar icon_ranges[] = { ICON_MIN_FA, ICON_MAX_FA, 0 };

    ImFontConfig imVerdanaConfig = {};
    imVerdanaConfig.FontBuilderFlags = ImGuiFreeTypeBuilderFlags_MonoHinting;
    imVerdanaConfig.OversampleH = 1;
    imVerdanaConfig.OversampleV = 1;
    imVerdanaConfig.PixelSnapH = true;

    Fonts::Default = io.Fonts->AddFontFromMemoryTTF(inter_bold, sizeof(inter_bold), 13.f, &cfg, io.Fonts->GetGlyphRangesCyrillic());
    Fonts::GunIcons = io.Fonts->AddFontFromMemoryTTF(GunIcons, sizeof(GunIcons), 12, &imVerdanaConfig, io.Fonts->GetGlyphRangesCyrillic());
    Fonts::ESP = io.Fonts->AddFontFromMemoryTTF(SmallestPixel, sizeof(SmallestPixel), 10, &imVerdanaConfig, io.Fonts->GetGlyphRangesCyrillic());
    
    // Replace Windows fonts with clean Inter font
    Fonts::Verdana = io.Fonts->AddFontFromMemoryTTF(inter_medium, sizeof(inter_medium), 18, &cfg, io.Fonts->GetGlyphRangesCyrillic());
    Fonts::VerdanaBold = io.Fonts->AddFontFromMemoryTTF(inter_bold, sizeof(inter_bold), 14, &cfg, io.Fonts->GetGlyphRangesCyrillic());
    Fonts::VerdanaEsp = io.Fonts->AddFontFromMemoryTTF(inter_medium, sizeof(inter_medium), 10, &cfg, io.Fonts->GetGlyphRangesCyrillic());
    Fonts::SegoeUIBold = io.Fonts->AddFontFromMemoryTTF(inter_bold, sizeof(inter_bold), 12, &cfg, io.Fonts->GetGlyphRangesCyrillic());
    Fonts::SegoeUIBold18 = io.Fonts->AddFontFromMemoryTTF(inter_bold, sizeof(inter_bold), 18, &cfg, io.Fonts->GetGlyphRangesCyrillic());
    Fonts::SegoeUIBold25 = io.Fonts->AddFontFromMemoryTTF(inter_bold, sizeof(inter_bold), 30, &cfg, io.Fonts->GetGlyphRangesCyrillic());
    Fonts::SegoeUIBold18_ITALIC = io.Fonts->AddFontFromMemoryTTF(inter_bold, sizeof(inter_bold), 18, &cfg, io.Fonts->GetGlyphRangesCyrillic());
    Fonts::overlay2 = io.Fonts->AddFontFromMemoryTTF(inter_medium, sizeof(inter_medium), 18, &cfg, io.Fonts->GetGlyphRangesCyrillic());

    Fonts::icons[0] = io.Fonts->AddFontFromMemoryTTF(icons_data, sizeof icons_data, 24, &cfg, icon_ranges);
    Fonts::logo = io.Fonts->AddFontFromMemoryTTF(icons_data, sizeof icons_data, 40, &cfg, icon_ranges);
    Fonts::icons[1] = io.Fonts->AddFontFromMemoryTTF(icons_data, sizeof icons_data, 15, &cfg, icon_ranges);
    Fonts::icons[2] = io.Fonts->AddFontFromMemoryTTF(icons_data, sizeof icons_data, 10, &cfg, icon_ranges);
    Fonts::inter[0] = io.Fonts->AddFontFromMemoryTTF(inter_bold, sizeof inter_bold, 15, &cfg, io.Fonts->GetGlyphRangesCyrillic());
    Fonts::inter[1] = io.Fonts->AddFontFromMemoryTTF(inter_medium, sizeof inter_medium, 13, &cfg, io.Fonts->GetGlyphRangesCyrillic());
    Fonts::inter[2] = io.Fonts->AddFontFromMemoryTTF(inter_bold, sizeof inter_bold, 13, &cfg, io.Fonts->GetGlyphRangesCyrillic());

    Fonts::overlay = io.Fonts->AddFontFromMemoryTTF(overlay, sizeof overlay, 13.5f, &cfg, io.Fonts->GetGlyphRangesDefault());

    ImFontConfig cfg2;
    cfg2.FontBuilderFlags = ImGuiFreeTypeBuilderFlags_MonoHinting;
    cfg2.PixelSnapH = true;

    Fonts::overlay3 = io.Fonts->AddFontFromMemoryTTF(overlay, sizeof overlay, 16, &cfg2, io.Fonts->GetGlyphRangesCyrillic());

    static const ImWchar arrIconRanges[] = { ICON_MIN_FA, ICON_MAX_FA, 0 };

    ImFontConfig imFAFontConfig;
    imFAFontConfig.MergeMode = true;
    imFAFontConfig.FontBuilderFlags |= ImGuiFreeTypeBuilderFlags_ForceAutoHint | ImGuiFreeTypeBuilderFlags_LoadColor | ImGuiFreeTypeBuilderFlags_Bitmap;
    Fonts::imFA = io.Fonts->AddFontFromMemoryCompressedTTF(FA_FONT, FA_compressed_size, 15.f, &imFAFontConfig, arrIconRanges);

    // Create standalone FontAwesome font for icon rendering (NOT merged - separate font)
    ImFontConfig iconFontConfig;
    iconFontConfig.MergeMode = false;  // Important: must be false for standalone icon font
    iconFontConfig.FontBuilderFlags = ImGuiFreeTypeBuilderFlags_ForceAutoHint;
    iconFontConfig.PixelSnapH = true;
    iconFontConfig.GlyphMinAdvanceX = 14.f;  // Consistent icon width
    iconFontConfig.GlyphMaxAdvanceX = 14.f;
    font::icon = io.Fonts->AddFontFromMemoryCompressedTTF(FA_FONT, FA_compressed_size, 14.f, &iconFontConfig, arrIconRanges);

    // Assign inter_bold for title text rendering
    font::inter_bold = Fonts::inter[0];  // Inter Bold 15px
    
    // Assign inter_default to bold font for consistent UI text
    font::inter_default = Fonts::inter[2];  // Inter Bold 13px

    // Build with force auto hinting for smooth text
    m_bInitialized = ImGuiFreeType::BuildFontAtlas(io.Fonts, 0);
}

void Gui::Update(ImGuiIO& io)
{
    Gui::m_vecScreenSize = io.DisplaySize;

    ImGuiStyle& style = ImGui::GetStyle();
    style.WindowBorderSize = 0.f;
    style.Alpha = 1.f;
}

void Gui::DrawHitLogs()
{
    if (!Config::b(g_Variables.m_Misc.m_bHitlogs))
        return;

    if (!Interfaces::m_pGlobalVariables || !Interfaces::m_pEngine->IsInGame() || !Interfaces::m_pEngine->IsConnected() || !Globals::m_pLocalPlayerController || !Globals::m_pLocalPlayerController->m_bPawnIsAlive()){
        if (!g_HitLogs.empty())
            g_HitLogs.clear();
        return;
    }

    ImDrawList* pDrawList = ImGui::GetBackgroundDrawList();
    float currentTime = Interfaces::m_pGlobalVariables->m_flCurrentTime;
    float messageHeight = 20.f;
    float startY = 5.0f;
    float startX = 5.0f;

    for (size_t i = 0; i < g_HitLogs.size(); ++i)
    {
        const HitLogEntry_t& entry = g_HitLogs[i];

        if (currentTime - entry.m_flStartTime > (entry.m_flFadeStartTime - entry.m_flStartTime + entry.m_flFadeDuration))
        {
            g_HitLogs.erase(g_HitLogs.begin() + i);
            continue;
        }

        float alpha = 1.0f;

        if (currentTime >= entry.m_flFadeStartTime)
        {
            alpha = 1.0f - ((currentTime - entry.m_flFadeStartTime) / entry.m_flFadeDuration);
            alpha = std::max(0.0f, std::min(1.0f, alpha));
        }

        ImColor color(255, 255, 255, static_cast<int>(255 * alpha));
        ImColor color2(0, 0, 0, static_cast<int>(100 * alpha));

        pDrawList->AddText(Fonts::Verdana, 18.f, ImVec2(startX + 1, startY + i * messageHeight + 1), color2, entry.m_strMessage.c_str());
        pDrawList->AddText(Fonts::Verdana, 18.f, ImVec2(startX, startY + i * messageHeight), color, entry.m_strMessage.c_str());
    }
}

static void RenderMenu()
{
    if (!Gui::m_bOpen)
        return;

    ImGuiStyle* style = &ImGui::GetStyle();

    // Push bold font for all menu text
    ImGui::PushFont(Fonts::Default);

    ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(0.f, 0.f));
    ImGui::PushStyleVar(ImGuiStyleVar_WindowBorderSize, 0.f);
    ImGui::PushStyleVar(ImGuiStyleVar_ItemSpacing, ImVec2(Layout::ItemSpacingX, Layout::ItemSpacingY));
    ImGui::PushStyleVar(ImGuiStyleVar_ScrollbarSize, 8.f);

    ImGui::SetNextWindowSize({ c::bg::size });

    ImGui::Begin("IMGUI", nullptr, ImGuiWindowFlags_NoBackground | ImGuiWindowFlags_NoDecoration);
    {
        const ImVec2& pos = ImGui::GetWindowPos();
        const ImVec2& spacing = style->ItemSpacing;
        const ImVec2& region = ImGui::GetContentRegionMax();

        // Window shadow (fatality-style depth)
        for (int i = 0; i < 4; i++) {
            float alpha = 12.f - i * 3.f;
            ImGui::GetBackgroundDrawList()->AddRect(
                pos - ImVec2((float)i, (float)i), 
                pos + region + ImVec2((float)i, (float)i),IM_COL32(0, 0, 0, (int)alpha), c::bg::rounding + i);
        }

        ImGui::GetBackgroundDrawList()->AddRectFilled(pos, pos + region, ImGui::GetColorU32(c::bg::background), c::bg::rounding);

        // Larger sidebar for better organization (200px)
        const float sidebar_width = Layout::SidebarWidth;
        const ImVec2 left_panel_min = pos + spacing;
        const ImVec2 left_panel_max = pos + ImVec2(sidebar_width, region.y - spacing.y);
        ImGui::GetBackgroundDrawList()->AddRectFilled(left_panel_min, left_panel_max, ImGui::GetColorU32(c::child::background), c::child::rounding);

        // Shadow line under sidebar header (fatality-style)
        ImGui::GetBackgroundDrawList()->AddRectFilledMultiColor(
            ImVec2(left_panel_min.x, left_panel_min.y + 75),
            ImVec2(left_panel_max.x, left_panel_min.y + 83),
            IM_COL32(0, 0, 0, 25), IM_COL32(0, 0, 0, 25),
            IM_COL32(0, 0, 0, 0), IM_COL32(0, 0, 0, 0));

        // Vertical separator line between sidebar and content
        ImGui::GetBackgroundDrawList()->AddLine(
            ImVec2(left_panel_max.x + spacing.x/2, pos.y + spacing.y + Layout::WindowPadding),
            ImVec2(left_panel_max.x + spacing.x/2, pos.y + region.y - spacing.y - Layout::WindowPadding),
            IM_COL32(255, 255, 255, 8), 1.0f);



        const ImVec2 header_min = pos + spacing;
        const ImVec2 logo_size(52.f, 52.f);
        const float logo_gap = Layout::ItemSpacingX;
        const float sidebar_content_width = sidebar_width - spacing.x * 2;
        // Calculate centered position for logo + text
        const char* displayName = GetCheatDisplayName();
        const float text_font_size = 28.f;  // Scaled up from 17
        const ImVec2 actual_text_size = ImGui::CalcTextSize(displayName);
        const float total_width = logo_size.x + logo_gap + (actual_text_size.x * (text_font_size / 13.f));
        const float center_x = header_min.x + (sidebar_content_width - total_width) * 0.5f;
        const ImVec2 logo_pos = ImVec2(center_x, header_min.y + 15.f);

        // Draw LUX logo image
        if (pic::lux_logo != nullptr)
        {
            ImGui::GetBackgroundDrawList()->AddImage((ImTextureID)pic::lux_logo, logo_pos, logo_pos + logo_size, ImVec2(0, 0), ImVec2(1, 1), IM_COL32_WHITE);
        }
        
        // Draw LUX text with subtle glitch effect (fatality-style)
        const ImVec2 text_pos = ImVec2(logo_pos.x + logo_size.x + logo_gap, logo_pos.y + (logo_size.y - text_font_size) * 0.5f);
        g_GlitchTimer += ImGui::GetIO().DeltaTime * 2.0f;
        float glitch_x = sinf(g_GlitchTimer) * 0.5f;
        float glitch_y = cosf(g_GlitchTimer) * 0.3f;
        
        // RGB offset layers (subtle)
        ImGui::GetBackgroundDrawList()->AddText(font::inter_bold, text_font_size, 
            text_pos + ImVec2(glitch_x, glitch_y), IM_COL32(255, 50, 50, 30), displayName);
        ImGui::GetBackgroundDrawList()->AddText(font::inter_bold, text_font_size, 
            text_pos + ImVec2(-glitch_x, -glitch_y), IM_COL32(50, 50, 255, 30), displayName);
        // Main text
        ImGui::GetBackgroundDrawList()->AddText(font::inter_bold, text_font_size, text_pos, ImGui::GetColorU32(c::accent), displayName);

        static int tabs = 0;
        static int aimbot_subtabs = 0;
        static int visuals_subtabs = 0;
        static int misc_subtabs = 0;
        static int settings_subtabs = 0;
        static int last_tabs = -1;

        if (tabs != last_tabs)
        {
            last_tabs = tabs;
        }

        // Tab dimensions for larger sidebar
        const float tab_width = sidebar_width - spacing.x * 3;
        const float subtab_width = tab_width -16.f;
        const float subtab_indent = 12.f;
        
        const ImVec2 tabs_pos = ImVec2(spacing.x * 2, 85);
        ImGui::SetCursorPos(tabs_pos);
        ImGui::BeginGroup();
        {
            // === AIMBOT TAB ===
            if (ImGui::Tabs(0 == tabs, "\xef\x81\x9b", "Aimbot", ImVec2(tab_width, 38), NULL)) { tabs = 0; }
            
            if (tabs == 0)
            {
                ImGui::SetCursorPosX(tabs_pos.x + subtab_indent);
                if (ImGui::SubTab(0 == aimbot_subtabs, "Rage", ImVec2(subtab_width, 30))) aimbot_subtabs = 0;
                ImGui::SetCursorPosX(tabs_pos.x + subtab_indent);
                if (ImGui::SubTab(1 == aimbot_subtabs, "Legit", ImVec2(subtab_width, 30))) aimbot_subtabs = 1;
                ImGui::SetCursorPosX(tabs_pos.x + subtab_indent);
                if (ImGui::SubTab(2 == aimbot_subtabs, "Anti-Aim", ImVec2(subtab_width, 30))) aimbot_subtabs = 2;
            }
            
            // === VISUALS TAB ===
            if (ImGui::Tabs(1 == tabs, "\xef\x81\xae", "Visuals", ImVec2(tab_width, 38), NULL)) { tabs = 1; }
            
            if (tabs == 1)
            {
                ImGui::SetCursorPosX(tabs_pos.x + subtab_indent);
                if (ImGui::SubTab(0 == visuals_subtabs, "Players", ImVec2(subtab_width, 30))) visuals_subtabs = 0;
                ImGui::SetCursorPosX(tabs_pos.x + subtab_indent);
                if (ImGui::SubTab(1 == visuals_subtabs, "World", ImVec2(subtab_width, 30))) visuals_subtabs = 1;
                ImGui::SetCursorPosX(tabs_pos.x + subtab_indent);
                if (ImGui::SubTab(2 == visuals_subtabs, "Chams", ImVec2(subtab_width, 30))) visuals_subtabs = 2;
                ImGui::SetCursorPosX(tabs_pos.x + subtab_indent);
                if (ImGui::SubTab(3 == visuals_subtabs, "Effects", ImVec2(subtab_width, 30))) visuals_subtabs = 3;
            }
            
            // === MISC TAB ===
            if (ImGui::Tabs(2 == tabs, "\xef\x80\x93", "Misc", ImVec2(tab_width, 38), NULL)) { tabs = 2; }

            if (tabs == 2)
            {
                ImGui::SetCursorPosX(tabs_pos.x + subtab_indent);
                if (ImGui::SubTab(0 == misc_subtabs, "Movement", ImVec2(subtab_width, 30))) misc_subtabs = 0;
                ImGui::SetCursorPosX(tabs_pos.x + subtab_indent);
                if (ImGui::SubTab(1 == misc_subtabs, "Gameplay", ImVec2(subtab_width, 30))) misc_subtabs = 1;
                ImGui::SetCursorPosX(tabs_pos.x + subtab_indent);
                if (ImGui::SubTab(2 == misc_subtabs, "HUD", ImVec2(subtab_width, 30))) misc_subtabs = 2;}
            
            // === SETTINGS TAB ===
            if (ImGui::Tabs(4 == tabs, "\xef\x80\x93", "Settings", ImVec2(tab_width, 38), NULL)) { tabs = 4; }

            if (tabs == 4)
            {
                ImGui::SetCursorPosX(tabs_pos.x + subtab_indent);
                if (ImGui::SubTab(0 == settings_subtabs, "Configs", ImVec2(subtab_width, 30))) settings_subtabs = 0;
                ImGui::SetCursorPosX(tabs_pos.x + subtab_indent);
                if (ImGui::SubTab(1 == settings_subtabs, "Menu", ImVec2(subtab_width, 30))) settings_subtabs = 1;
            }
        }
        ImGui::EndGroup();

        // User Info Panel - always show (with fallback when not authenticated)
        {
            const float userPanelHeight = 90.f;  // Increased height for larger text
            const ImVec2 userPanel_min = ImVec2(pos.x + spacing.x + 10, pos.y + region.y - spacing.y - userPanelHeight);
            
            // Separator line above user panel
            ImGui::GetBackgroundDrawList()->AddLine(
                ImVec2(userPanel_min.x, userPanel_min.y - 8),
                ImVec2(pos.x + sidebar_width - spacing.x - 10, userPanel_min.y - 8),
                IM_COL32(255, 255, 255, 15), 1.0f);
            
            float yOffset = 0.f;
            
            if (UserData::g_IsAuthenticated && !UserData::g_Username.empty()) {
                // Username with subtle accent - larger font
                std::string displayName = UserData::g_Username;
                if (displayName.length() > 16) displayName = displayName.substr(0, 14) + "..";
                ImGui::GetBackgroundDrawList()->AddText(font::inter_bold, 18.f,  // Increased from 14
                    userPanel_min, ImGui::GetColorU32(c::accent), displayName.c_str());
                yOffset = 22.f;  // Increased from 18
                
                // Subscription type - larger font
                if (!UserData::g_Subscription.empty()) {
                    ImGui::GetBackgroundDrawList()->AddText(font::inter_default, 14.f,  // Increased from 11
                        ImVec2(userPanel_min.x, userPanel_min.y + yOffset),
                        ImGui::GetColorU32(ImVec4(0.6f, 0.6f, 0.6f, 0.9f)), UserData::g_Subscription.c_str());
                    yOffset += 18.f;  // Increased from 14
                }
                
                // Time remaining - larger font
                if (UserData::g_TimeLeft > 0) {
                    std::string timeLeft = "Time: " + UserData::GetTimeLeftFormatted();
                    ImGui::GetBackgroundDrawList()->AddText(font::inter_default, 14.f,  // Increased from 11
                        ImVec2(userPanel_min.x, userPanel_min.y + yOffset),
                        ImGui::GetColorU32(ImVec4(0.5f, 0.8f, 0.5f, 0.9f)), timeLeft.c_str());
                    yOffset += 18.f;  // Increased from 14
                }
                
                // Expiry date - larger font
                std::string expiry = UserData::GetExpiryFormatted();
                if (!expiry.empty()) {
                    std::string expiryText = "Expires: " + expiry;
                    ImGui::GetBackgroundDrawList()->AddText(font::inter_default, 13.f,  // Increased from 10
                        ImVec2(userPanel_min.x, userPanel_min.y + yOffset),
                        ImGui::GetColorU32(ImVec4(0.4f, 0.4f, 0.4f, 0.7f)), expiryText.c_str());
                }
            }
            else {
                // Fallback display when not authenticated - larger fonts
                ImGui::GetBackgroundDrawList()->AddText(font::inter_bold, 24.f,  // Increased from 20
                    userPanel_min, ImGui::GetColorU32(c::accent), "Guest");
                yOffset = 28.f;  // Increased from 24
                
                ImGui::GetBackgroundDrawList()->AddText(font::inter_default, 18.f,  // Increased from 16
                    ImVec2(userPanel_min.x, userPanel_min.y + yOffset),
                    ImGui::GetColorU32(ImVec4(0.5f, 0.5f, 0.5f, 0.8f)), "Not authenticated");
                yOffset += 22.f;  // Increased from 20
                
                ImGui::GetBackgroundDrawList()->AddText(font::inter_default, 16.f,  // Increased from 14
                    ImVec2(userPanel_min.x, userPanel_min.y + yOffset),
                    ImGui::GetColorU32(ImVec4(0.4f, 0.4f, 0.4f, 0.6f)), "Use launcher to login");
            }
        }

        // Keyboard shortcuts only (minimal - no toolbar)
        if (ImGui::GetIO().KeyCtrl && ImGui::IsKeyPressed(ImGuiKey_S)) {
            if (!Config::vecFileNames.empty()) {
                Config::Save(Config::vecFileNames[0], true);
                g_SaveNotifyTimer = 2.0f;
            }
        }
        // Save notification - minimal toast with fade
        if (g_SaveNotifyTimer > 0.f) {
            g_SaveNotifyTimer -= ImGui::GetIO().DeltaTime;
            float alpha = std::min(g_SaveNotifyTimer, 1.0f);
            float slide = (1.0f - alpha) * 10.f;
            ImVec2 notifyPos = ImVec2(pos.x + region.x - 60+ slide, pos.y + spacing.y + 8);
            
            // Background pill
            ImVec2 pillSize(50, 18);
            ImGui::GetForegroundDrawList()->AddRectFilled(
                notifyPos - ImVec2(5, 2), notifyPos + pillSize,IM_COL32(40, 180, 80, (int)(alpha * 200)), 4.f);
            
            ImGui::GetForegroundDrawList()->AddText(font::inter_default, 11.f, 
                notifyPos + ImVec2(8, 2),IM_COL32(255, 255, 255, (int)(alpha * 255)), "Saved");
        }

        // Content area starts after sidebar
        const float content_start_x = sidebar_width + spacing.x * 2;
        const float content_width = region.x - content_start_x - spacing.x;
        const float content_height = region.y - spacing.y * 2;
        const float group_width = (content_width - spacing.x) / 2.0f;
        const float group_height_full = content_height - 10.f;
        const float group_height_half = (group_height_full - spacing.y) / 2.0f;
        
        ImGui::SetCursorPos(ImVec2(content_start_x, spacing.y));

        // === RAGE TAB CONTENT ===
        if (tabs == 0) {
            if (aimbot_subtabs == 0) {
                // Calculate dynamic heights for better content fit
                const float rage_general_h = 340.f;
                const float rage_accuracy_h = group_height_full - rage_general_h - Layout::ItemSpacingY;

                ImGui::BeginGroup();
                {
                    ImGui::CustomBeginChild("GENERAL", ImVec2(group_width, rage_general_h));
                    {
                                ApraGui::SectionHeader("Ragebot Core");
                                ApraGui::ToggleSwitch("Enabled", &Config::b(g_Variables.m_Ragebot.m_bRagebotEnabled));
                        ImGui::BeginDisabled(!Config::b(g_Variables.m_Ragebot.m_bRagebotEnabled));
                        {
                            ImGui::Spacing();
                                        ApraGui::CheckBox(X("Automatic Fire"), &Config::b(g_Variables.m_Ragebot.m_bAutoShoot));
                                        ApraGui::CheckBox(X("Silent Aimbot"), &Config::b(g_Variables.m_Ragebot.m_bSilentAim));
                                        ApraGui::CheckBox(X("Auto Revolver"), &Config::b(g_Variables.m_Ragebot.m_bAutoRevolver));        
                            ImGui::Spacing();ApraGui::SectionHeader("Accuracy Exploits");
                                        ApraGui::CheckBox(X("No Spread"), &Config::b(g_Variables.m_Ragebot.m_bNoSpread));
                                        if (ImGui::IsItemHovered()) ImGui::SetTooltip("Compensate weapon spread for perfect accuracy");
                                        if (Config::b(g_Variables.m_Ragebot.m_bNoSpread)) {
                                            ImGui::Keybind(X("No Spread Key"), (int*)&Config::kb(g_Variables.m_Ragebot.m_iNoSpreadKey).m_iKey, (int*)&Config::kb(g_Variables.m_Ragebot.m_iNoSpreadKey).m_iMode, true);
                                        }
                                        ApraGui::CheckBox(X("No Recoil"), &Config::b(g_Variables.m_Ragebot.m_bNoRecoil));
                                        if (ImGui::IsItemHovered()) ImGui::SetTooltip("Compensate recoil server-side - shots hit target without visual crosshair movement");
                            ImGui::SliderFloat(X("Aimbot FOV"), &Config::f(g_Variables.m_Ragebot.m_flFov), 1.f, 180.f, "%.1f");
                            ImGui::Keybind(X("Force Headshot"), (int*)&Config::kb(g_Variables.m_Ragebot.m_iForceHeadshotKey).m_iKey, (int*)&Config::kb(g_Variables.m_Ragebot.m_iForceHeadshotKey).m_iMode, true);
                            ImGui::Keybind(X("Force Bodyaim"), (int*)&Config::kb(g_Variables.m_Ragebot.m_iForceBodyaimKey).m_iKey, (int*)&Config::kb(g_Variables.m_Ragebot.m_iForceBodyaimKey).m_iMode, true);
                        }
                        ImGui::EndDisabled();
                    }
                    ImGui::CustomEndChild();

                    ImGui::CustomBeginChild("ACCURACY", ImVec2(group_width, rage_accuracy_h));
                    {
                        ApraGui::SectionHeader("Accuracy Settings");
                        static const char* modes[] = { "Early", "In Air" };

                        ApraGui::ToggleSwitch(X("Auto Stop"), &Config::b(g_Variables.m_Ragebot.m_bAutostop));
                        if (ImGui::IsItemHovered()) ImGui::SetTooltip("Stop movement for better accuracy");
                        ImGui::BeginDisabled(!Config::b(g_Variables.m_Ragebot.m_bAutostop));
                        {
                            ImGui::MultiCombo(X("Auto Stop Modifiers"), Config::vb(g_Variables.m_Ragebot.m_vbAutostop), modes, IM_ARRAYSIZE(modes));
                        }
                        ImGui::EndDisabled();

                        ImGui::Spacing();
                        ImGui::Separator();
                        ImGui::Spacing();
                        ApraGui::SectionHeaderPlus("Quick Peek Assist");
                        
                        Color& peekColor = Config::c(g_Variables.m_Ragebot.m_AutoPeekColor);
                        float colorArray[4] = {
                            peekColor.rBase(),
                            peekColor.gBase(),
                            peekColor.bBase(),
                            peekColor.aBase()
                        };

                        ImGui::Pickerbox(X("Enable Quick Peek"), &Config::b(g_Variables.m_Ragebot.m_bAutoPeek), colorArray, picker_flags);
                        peekColor.Set(colorArray[0], colorArray[1], colorArray[2], colorArray[3]);

                        ImGui::BeginDisabled(!Config::b(g_Variables.m_Ragebot.m_bAutoPeek));
                        {
                            ApraGui::CheckBox(X("Retreat On Key Release"), &Config::b(g_Variables.m_Ragebot.m_bRetreatOnRelease));
                            ImGui::Keybind(X("Quick Peek Key"), (int*)&Config::kb(g_Variables.m_Ragebot.m_kbAutoPeek).m_iKey, (int*)&Config::kb(g_Variables.m_Ragebot.m_kbAutoPeek).m_iMode, true);
                        }
                        ImGui::EndDisabled();
                    }
                    ImGui::CustomEndChild();
                }
                ImGui::EndGroup();

                ImGui::SameLine();

                ImGui::BeginGroup();
                {
                    ImGui::CustomBeginChild("WEAPON CONFIG", ImVec2(group_width, group_height_full));
                    {
                        ApraGui::SectionHeader("Weapon Settings");
                        static const char* target_select[] = { "Damage", "Hit-chance", "Health" };
                        static const char* weapon_names[] = { ("Pistols"), ("Deagle"), ("Revolver"), ("SMGs"), ("LMGs"), ("Rifles"), ("Shotguns"), ("SSG-08"), ("Auto-snipers"), ("AWP") };
                        static const char* hb[] = { "Head", "Chest", "Stomatch", "Pelvis", "Arms", "Legs", "Feet" };
                        static bool hitbox_selections[HB_MAX] = { false };
                        static bool multipoint_selections[HB_MAX] = { false };
                        static int lastWeaponType = -1;

                        // Global Hitboxes - always accessible, independent of weapon configs
                        ApraGui::SectionHeader("Hitboxes");
                        auto& globalHitboxes = GetGlobalHitboxes();

                        // Sync hitbox selections from global config (only once on first load)
                        static bool hitboxesInitialized = false;
                        if (!hitboxesInitialized) {
                            hitboxesInitialized = true;
                            memset(hitbox_selections, 0, sizeof(hitbox_selections));
                            memset(multipoint_selections, 0, sizeof(multipoint_selections));

                            for (const auto& hitbox : globalHitboxes) {
                                hitbox_selections[hitbox.m_eDefinition] = true;
                                multipoint_selections[hitbox.m_eDefinition] = hitbox.m_bMultipoint;
                            }
                        }

                        std::vector<bool> hitbox_vars;
                        for (int i = 0; i < HB_MAX; i++) {
                            hitbox_vars.push_back(hitbox_selections[i]);
                        }
                        ImGui::MultiCombo("Hitboxes", hitbox_vars, hb, HB_MAX);

                        bool any_hitbox_selected = std::any_of(hitbox_vars.begin(), hitbox_vars.end(), [](bool selected) { return selected; });

                        std::vector<bool> multipoint_vars;
                        for (int i = 0; i < HB_MAX; i++) {
                            multipoint_vars.push_back(multipoint_selections[i]);
                        }

                        ImGui::BeginDisabled(!any_hitbox_selected);
                        ImGui::MultiCombo("Multipoints", multipoint_vars, hb, HB_MAX);
                        ImGui::EndDisabled();

                        for (int i = 0; i < HB_MAX; i++) {
                            hitbox_selections[i] = hitbox_vars[i];
                            multipoint_selections[i] = multipoint_vars[i];
                        }

                        globalHitboxes.erase(
                            std::remove_if(globalHitboxes.begin(), globalHitboxes.end(),
                                [&](const MenuRageHitbox_t& h) {
                                    return !hitbox_selections[h.m_eDefinition];
                                }),
                            globalHitboxes.end()
                        );

                        for (int i = 0; i < HB_MAX; i++) {
                            if (hitbox_selections[i] &&
                                std::find_if(globalHitboxes.begin(), globalHitboxes.end(),
                                    [i](const MenuRageHitbox_t& h) { return h.m_eDefinition == i; }) == globalHitboxes.end()) {
                                globalHitboxes.push_back({ multipoint_selections[i], static_cast<EConfigHitBoxes>(i) });
                            }
                        }

                        for (auto& hitbox : globalHitboxes) {
                            hitbox.m_bMultipoint = multipoint_selections[hitbox.m_eDefinition];
                        }

                        ImGui::Separator();
                        // Weapon Configs - can be disabled independently
                        ApraGui::SectionHeader("Weapon Config");
                        ApraGui::CheckBox("Enable Weapon Configs", &Config::b(g_Variables.m_Ragebot.m_bWeaponConfigsEnabled));
                        if (ImGui::IsItemHovered()) ImGui::SetTooltip("When disabled, ragebot won't work");
                        ImGui::BeginDisabled(!Config::b(g_Variables.m_Ragebot.m_bWeaponConfigsEnabled));
                        ImGui::Combo(X("Edit Config"), &currentWeaponType, weapon_names, IM_ARRAYSIZE(weapon_names));
                        ApraGui::CheckBox("Enable This Config", &GetWeaponEnabledToggle());
                        if (ImGui::IsItemHovered()) ImGui::SetTooltip("When disabled, ragebot won't work with this weapon");

                        ApraGui::SectionHeader("Accuracy");
                        ImGui::SliderInt(X("Point Scale"), &GetPointscaleForCurrentWeapon(), 0, 100, FormatPointscale(GetPointscaleForCurrentWeapon()));

                        ImGui::SliderInt(X("Hit Chance"), &GetHitchanceForCurrentWeapon(), 0, 100, "%d%%");
                        ImGui::SliderInt(X("Hit Chance Override"), &GetHitchanceOverrideForCurrentWeapon(), 0, 100, "%d%%");
                        ImGui::Keybind(X("Override Hitchance Key"), (int*)&Config::kb(g_Variables.m_Ragebot.m_iHitchanceOverrideKey).m_iKey, (int*)&Config::kb(g_Variables.m_Ragebot.m_iHitchanceOverrideKey).m_iMode, true);

                        ApraGui::SectionHeader("Damage");
                        ImGui::SliderInt(X("Minimum Damage"), &GetMinDamageForCurrentWeapon(), 1, 120, FormatDamageValue(GetMinDamageForCurrentWeapon()));

                        ImGui::SliderInt(X("Override Damage"), &GetMinDamageOverrideForCurrentWeapon(), 1, 120, FormatDamageValue(GetMinDamageOverrideForCurrentWeapon()));

                        ImGui::Keybind(X("Override Damage Key"), (int*)&Config::kb(g_Variables.m_Ragebot.m_iMinDamageOverrideKey).m_iKey, (int*)&Config::kb(g_Variables.m_Ragebot.m_iMinDamageOverrideKey).m_iMode, true);

                        // Jump Scout option - only show for SSG-08 (index 7)
                        if (currentWeaponType == 7) {
                            ApraGui::SectionHeader("Scout Options");
                            ApraGui::CheckBox("Jump Scout", &Config::b(g_Variables.m_Ragebot.m_bJumpScout));
                            if (ImGui::IsItemHovered()) ImGui::SetTooltip("Allow accurate shots at jump apex with SSG-08");
                        }
                        ImGui::EndDisabled();    }
                    ImGui::CustomEndChild();
                }
                ImGui::EndGroup();
            }
            else if (aimbot_subtabs == 1) {
                // Calculate dynamic heights for Legitbot - match Rage layout
                const float legit_general_h = 180.f;
                const float legit_trigger_h = group_height_full - legit_general_h - Layout::ItemSpacingY;

                ImGui::BeginGroup();
                {
                    ImGui::CustomBeginChild("GENERAL", ImVec2(group_width, legit_general_h));
                    {
                        ApraGui::SectionHeader("Legitbot Core");
                        ApraGui::ToggleSwitch("Enabled", &Config::b(g_Variables.m_Legitbot.m_bEnabled));
                        ImGui::BeginDisabled(!Config::b(g_Variables.m_Legitbot.m_bEnabled));
                        {
                            ImGui::Spacing();
                            ImGui::Keybind("Aim Key", (int*)&Config::kb(g_Variables.m_Legitbot.m_iAimKey).m_iKey, (int*)&Config::kb(g_Variables.m_Legitbot.m_iAimKey).m_iMode, true);
                            static const char* conditions[] = { "Visible Only", "Ignore Smoke", "Ignore Flash" };
                            ImGui::MultiCombo(X("Conditions"), Config::vb(g_Variables.m_Legitbot.m_vbConditions), conditions, ELegitbotConditions::CONDITION_MAX);
                        }
                        ImGui::EndDisabled();
                    }
                    ImGui::CustomEndChild();

                    ImGui::CustomBeginChild("TRIGGERBOT", ImVec2(group_width, legit_trigger_h));
                    {
                        ApraGui::SectionHeader("Triggerbot");
                        ApraGui::ToggleSwitch("Enable Triggerbot", &Config::b(g_Variables.m_Legitbot.m_bTriggerbot));
                        ImGui::BeginDisabled(!Config::b(g_Variables.m_Legitbot.m_bTriggerbot));
                        {
                            ImGui::Keybind("Trigger Key", (int*)&Config::kb(g_Variables.m_Legitbot.m_iTriggerKey).m_iKey, (int*)&Config::kb(g_Variables.m_Legitbot.m_iTriggerKey).m_iMode, true);
                            ImGui::SliderInt(X("Trigger Delay (ms)"), &Config::i(g_Variables.m_Legitbot.m_iTriggerDelay), 0, 500);
                            ApraGui::CheckBox("Trigger Magnet", &Config::b(g_Variables.m_Legitbot.m_bTriggerMagnet));
                            if (ImGui::IsItemHovered()) ImGui::SetTooltip("Pull aim towards target when triggered");
                            ApraGui::CheckBox("Head Only", &Config::b(g_Variables.m_Legitbot.m_bTriggerHeadOnly));
                        }
                        ImGui::EndDisabled();
                    }
                    ImGui::CustomEndChild();
                }
                ImGui::EndGroup();

                ImGui::SameLine();

                ImGui::BeginGroup();
                {
                    ImGui::CustomBeginChild("WEAPON CONFIG", ImVec2(group_width, group_height_full));
                    {
                        static const char* weapon_configs[] = { "Pistols", "Deagle", "Revolver", "SMGs", "LMGs", "Rifles", "Shotguns", "SSG-08", "Auto-snipers", "AWP" };
                        static const char* smooth_types[] = { "Linear", "Ease-out", "Ease-in-out" };
                        static const char* target_priority[] = { "Crosshair", "Lowest HP", "Distance" };
                        static const char* legit_hb[] = { "Head", "Chest", "Stomach", "Pelvis", "Arms", "Legs", "Feet" };

                        // Hitboxes section - always accessible like Rage
                        ApraGui::SectionHeader("Hitboxes");
                        ImGui::BeginDisabled(!Config::b(g_Variables.m_Legitbot.m_bEnabled));
                        ImGui::MultiCombo(X("Hitboxes"), Config::vb(g_Variables.m_Legitbot.m_vbHitboxes), legit_hb, ELegitbotHitboxes::LEGITHITBOX_MAX);
                        ImGui::EndDisabled();

                        ImGui::Separator();

                        // Weapon Configs section - matches Rage layout
                        ApraGui::SectionHeader("Weapon Config");
                        ImGui::BeginDisabled(!Config::b(g_Variables.m_Legitbot.m_bEnabled));
                        {
                            ApraGui::CheckBox("Enable Weapon Configs", &Config::b(g_Variables.m_Legitbot.m_bWeaponConfigsEnabled));
                            if (ImGui::IsItemHovered()) ImGui::SetTooltip("Master toggle - when disabled, legitbot won't work");
                            ImGui::BeginDisabled(!Config::b(g_Variables.m_Legitbot.m_bWeaponConfigsEnabled));
                            ImGui::Combo(X("Edit Config"), &Config::i(g_Variables.m_Legitbot.m_iWeaponConfig), weapon_configs, IM_ARRAYSIZE(weapon_configs));
                            int cfg = Config::i(g_Variables.m_Legitbot.m_iWeaponConfig);
                            static bool legitWeaponEnabled = true;
                            legitWeaponEnabled = Config::vb(g_Variables.m_Legitbot.m_vbWeaponEnabled)[cfg];
                            if (ApraGui::CheckBox("Enable This Config", &legitWeaponEnabled)) {
                                Config::vb(g_Variables.m_Legitbot.m_vbWeaponEnabled)[cfg] = legitWeaponEnabled;
                            }
                            if (ImGui::IsItemHovered()) ImGui::SetTooltip("When disabled, legitbot won't work with this weapon");

                            ApraGui::SectionHeader("Targeting");
                            ImGui::Combo(X("Target Priority"), &Config::i(g_Variables.m_Legitbot.m_iTargetPriority), target_priority, IM_ARRAYSIZE(target_priority));
                            ImGui::SliderInt(X("FOV"), &Config::vi(g_Variables.m_Legitbot.m_viFov)[cfg], 1, 40);
                            ImGui::SliderInt(X("Smoothness"), &Config::vi(g_Variables.m_Legitbot.m_viSmoothness)[cfg], 1, 20);
                            ApraGui::CheckBox("Aim Pitch", &Config::b(g_Variables.m_Legitbot.m_bAimPitch));
                            ImGui::Combo(X("Smooth Type"), &Config::i(g_Variables.m_Legitbot.m_iSmoothType), smooth_types, IM_ARRAYSIZE(smooth_types));
                            ImGui::SliderInt(X("Randomization"), &Config::i(g_Variables.m_Legitbot.m_iRandomization), 0, 50, "%d%%");
                            
                            Color& fovCircleColor = Config::c(g_Variables.m_Legitbot.m_colFovCircle);
                            float fovCircleArr[4] = { fovCircleColor.rBase(), fovCircleColor.gBase(), fovCircleColor.bBase(), fovCircleColor.aBase() };
                            ImGui::Pickerbox("FOV Circle", &Config::b(g_Variables.m_Legitbot.m_bDrawFovCircle), fovCircleArr, picker_flags);
                            fovCircleColor.Set(fovCircleArr[0], fovCircleArr[1], fovCircleArr[2], fovCircleArr[3]);

                            ApraGui::SectionHeader("Recoil Control");
                            ApraGui::CheckBox("Enable RCS", &Config::b(g_Variables.m_Legitbot.m_bRCS));
                            ImGui::BeginDisabled(!Config::b(g_Variables.m_Legitbot.m_bRCS));
                            {
                                ApraGui::CheckBox("Silent RCS", &Config::b(g_Variables.m_Legitbot.m_bSilentRCS));
                                if (ImGui::IsItemHovered()) ImGui::SetTooltip("Compensate recoil server-side - shots hit without visual crosshair movement");ImGui::SliderInt(X("RCS X"), &Config::vi(g_Variables.m_Legitbot.m_viRCSX)[cfg], 0, 100, "%d%%");
                                ImGui::SliderInt(X("RCS Y"), &Config::vi(g_Variables.m_Legitbot.m_viRCSY)[cfg], 0, 100, "%d%%");
                                ImGui::SliderInt(X("RCS Start Bullet"), &Config::i(g_Variables.m_Legitbot.m_iRCSStart), 1, 10);
                                if (ImGui::IsItemHovered()) ImGui::SetTooltip("Start RCS after this many bullets");
                            }
                            ImGui::EndDisabled();
                            ImGui::EndDisabled();
                        }
                        ImGui::EndDisabled();
                    }
                    ImGui::CustomEndChild();
                }
                ImGui::EndGroup();
            }
            // ANTI-AIM SUBTAB
            else if (aimbot_subtabs == 2) {
                // Calculate dynamic heights for Anti-Aim
                const float aa_angles_h = 320.f;
                const float aa_misc_h = group_height_full - aa_angles_h - 12.f;

                ImGui::BeginGroup();
                {
                    ImGui::CustomBeginChild("ANGLES", ImVec2(group_width, aa_angles_h));
                    {
                        const char* pitch[] = { "None", "Down", "Up", "Zero", "Custom" };
                        const char* yaw[] = { "None", "Backward", "Custom" };
                        const char* yaw_base[] = { "Viewangles", "At Target" };
                        const char* yaw_jitter[] = { "None", "Center", "Offset" };

                        ApraGui::SectionHeader("Anti-Aim Core");
                        ImGui::Spacing();
                        ApraGui::CheckBox(X("Enabled"), &Config::b(g_Variables.m_Ragebot.m_bAntiaim));
                        
                        ImGui::BeginDisabled(!Config::b(g_Variables.m_Ragebot.m_bAntiaim));
                        {
                            ImGui::Spacing();
                            ApraGui::SectionHeader("Pitch Settings");
                            ImGui::Spacing();
                            ImGui::Combo("Pitch", &Config::i(g_Variables.m_Ragebot.m_iPitchValue), pitch, IM_ARRAYSIZE(pitch));
                            
                            ImGui::Spacing();
                            ApraGui::SectionHeader("Yaw Settings");
                            ImGui::Spacing();
                            ImGui::Combo("Yaw Base", &Config::i(g_Variables.m_Ragebot.m_iYawBase), yaw_base, IM_ARRAYSIZE(yaw_base));
                            ImGui::Combo("Yaw", &Config::i(g_Variables.m_Ragebot.m_iYawValue), yaw, IM_ARRAYSIZE(yaw));
                            ImGui::Combo("Yaw Jitter", &Config::i(g_Variables.m_Ragebot.m_iYawJitter), yaw_jitter, IM_ARRAYSIZE(yaw_jitter));
                            ImGui::Keybind("Freestanding", (int*)&Config::kb(g_Variables.m_Ragebot.m_bFreestanding).m_iKey, (int*)&Config::kb(g_Variables.m_Ragebot.m_bFreestanding).m_iMode, true);
                            ApraGui::CheckBox("At Targets", &Config::b(g_Variables.m_Ragebot.m_bAtTarget));
                        }
                        ImGui::EndDisabled();
                    }
                    ImGui::CustomEndChild();

                    ImGui::CustomBeginChild("MOVEMENT", ImVec2(group_width, aa_misc_h));
                    {
                        ApraGui::SectionHeader("Movement Options");
                        ImGui::Keybind("Slow Walk", (int*)&Config::kb(g_Variables.m_Ragebot.m_bAccurateWalk).m_iKey, (int*)&Config::kb(g_Variables.m_Ragebot.m_bAccurateWalk).m_iMode, true);
                        if (ImGui::IsItemHovered()) ImGui::SetTooltip("Walk slowly for better accuracy");
                    }
                    ImGui::CustomEndChild();
                }
                ImGui::EndGroup();

                ImGui::SameLine();

                ImGui::BeginGroup();
                {
                    ImGui::CustomBeginChild("MANUAL OVERRIDE", ImVec2(group_width, 160.f));
                    {
                        ApraGui::SectionHeader("Manual Direction");
                        ImGui::BeginDisabled(!Config::b(g_Variables.m_Ragebot.m_bAntiaim));
                        {
                            ImGui::Keybind("Manual Left", (int*)&Config::kb(g_Variables.m_Ragebot.m_iLeftAntiaimKeybind).m_iKey, (int*)&Config::kb(g_Variables.m_Ragebot.m_iLeftAntiaimKeybind).m_iMode, true);
                            ImGui::Keybind("Manual Right", (int*)&Config::kb(g_Variables.m_Ragebot.m_iRightAntiaimKeybind).m_iKey, (int*)&Config::kb(g_Variables.m_Ragebot.m_iRightAntiaimKeybind).m_iMode, true);
                            ImGui::Keybind("Manual Back", (int*)&Config::kb(g_Variables.m_Ragebot.m_iBackAntiaimKeybind).m_iKey, (int*)&Config::kb(g_Variables.m_Ragebot.m_iBackAntiaimKeybind).m_iMode, true);
                        }
                        ImGui::EndDisabled();
                    }
                    ImGui::CustomEndChild();

                    ImGui::CustomBeginChild("FAKE ANGLES", ImVec2(group_width, group_height_full - 160.f - 12.f));
                    {
                        ApraGui::SectionHeader("Desync Settings");
                        ImGui::BeginDisabled(!Config::b(g_Variables.m_Ragebot.m_bAntiaim));
                        {
                            ImGui::Keybind("Fake Pitch", (int*)&Config::kb(g_Variables.m_Ragebot.m_iFakePitch).m_iKey, (int*)&Config::kb(g_Variables.m_Ragebot.m_iFakePitch).m_iMode, true);
                            if (ImGui::IsItemHovered()) ImGui::SetTooltip("Toggle fake pitch angle");
                        }
                        ImGui::EndDisabled();
                    }
                    ImGui::CustomEndChild();
                }
                ImGui::EndGroup();
            }
        }
        else if (tabs == 1) {
            // VISUALS - PLAYERS SUBTAB
            if (visuals_subtabs == 0) {
                // Calculate dynamic heights
                const float effects_h = 125.f;
                const float ambience_h = group_height_full - effects_h - Layout::ItemSpacingY;

                const float grenades_h = 140.f;
                const float tracers_h = 170.f;
                const float feedback_h = group_height_full - grenades_h - tracers_h - Layout::ItemSpacingY * 2;

                ImGui::BeginGroup();
                {
                    ImGui::CustomBeginChild("AMBIENCE", ImVec2(group_width, ambience_h));
                    {
                        ApraGui::SectionHeader("World Colors");
                        Color& modelColor = Config::c(g_Variables.m_WorldVisuals.m_colWorldColor);
                        float colorArray[4] = {
                            modelColor.rBase(),
                            modelColor.gBase(),
                            modelColor.bBase(),
                            modelColor.aBase()
                        };

                        if (ImGui::ColorEdit3("World Color", colorArray, picker_flags))
                            modelColor.Set(colorArray[0], colorArray[1], colorArray[2], colorArray[3]);

                        Color& modelColor1 = Config::c(g_Variables.m_WorldVisuals.m_colLighting);
                        float colorArray1[4] = {
                            modelColor1.rBase(),
                            modelColor1.gBase(),
                            modelColor1.bBase(),
                            modelColor1.aBase()
                        };

                        if (ImGui::ColorEdit3(X("Lighting Color"), colorArray1, picker_flags))
                            modelColor1.Set(colorArray1[0], colorArray1[1], colorArray1[2], colorArray1[3]);

                        Color& modelColor12 = Config::c(g_Variables.m_WorldVisuals.m_colSunColor);
                        float colorArray12[4] = {
                            modelColor12.rBase(),
                            modelColor12.gBase(),
                            modelColor12.bBase(),
                            modelColor12.aBase()
                        };

                        if (ImGui::ColorEdit3(X("Sun Color"), colorArray12, picker_flags))
                            modelColor12.Set(colorArray12[0], colorArray12[1], colorArray12[2], colorArray12[3]);

                        Color& modelColor124 = Config::c(g_Variables.m_WorldVisuals.m_colCloudColor);
                        float colorArray124[4] = {
                            modelColor124.rBase(),
                            modelColor124.gBase(),
                            modelColor124.bBase(),
                            modelColor124.aBase()
                        };

                        if (ImGui::ColorEdit3(X("Clouds Color"), colorArray124, picker_flags))
                            modelColor124.Set(colorArray124[0], colorArray124[1], colorArray124[2], colorArray124[3]);

                        Color& modelColor123 = Config::c(g_Variables.m_WorldEffects.m_colSkybox);
                        float colorArray123[4] = {
                            modelColor123.rBase(),
                            modelColor123.gBase(),
                            modelColor123.bBase(),
                            modelColor123.aBase()
                        };

                        if (ImGui::ColorEdit3(X("Sky Color"), colorArray123, picker_flags))
                            modelColor123.Set(colorArray123[0], colorArray123[1], colorArray123[2], colorArray123[3]);

                        ImGui::SliderInt("Exposure", &Config::i(g_Variables.m_WorldVisuals.m_nWorldExposure), 0, 200);
                        
                        // Darkmode
                        ApraGui::ToggleSwitch("Darkmode", &Config::b(g_Variables.m_WorldEffects.m_bDarkMode));
                        ImGui::BeginDisabled(!Config::b(g_Variables.m_WorldEffects.m_bDarkMode));
                        {
                            ImGui::SliderInt("Darkness Amount", &Config::i(g_Variables.m_WorldEffects.m_iDarkModeAmount), 0, 100, "%d%%");
                            
                            Color& darkWorldCol = Config::c(g_Variables.m_WorldEffects.m_colDarkModeWorld);
                            float darkWorldArr[4] = { darkWorldCol.rBase(), darkWorldCol.gBase(), darkWorldCol.bBase(), darkWorldCol.aBase() };
                            if (ImGui::ColorEdit4("World Color##dark", darkWorldArr, picker_flags))
                                darkWorldCol.Set(darkWorldArr[0], darkWorldArr[1], darkWorldArr[2], darkWorldArr[3]);
                            
                            Color& darkPropCol = Config::c(g_Variables.m_WorldEffects.m_colDarkModeProp);
                            float darkPropArr[4] = { darkPropCol.rBase(), darkPropCol.gBase(), darkPropCol.bBase(), darkPropCol.aBase() };
                            if (ImGui::ColorEdit4("Prop Color##dark", darkPropArr, picker_flags))
                                darkPropCol.Set(darkPropArr[0], darkPropArr[1], darkPropArr[2], darkPropArr[3]);
                        }
                        ImGui::EndDisabled();
                        
                        ImGui::SliderInt("Prop Transparency", &Config::i(g_Variables.m_WorldEffects.m_iPropTransparency), 0, 100, "%d%%");
                    }
                    ImGui::CustomEndChild();

                    ImGui::CustomBeginChild("EFFECTS", ImVec2(group_width, effects_h));
                    {
                        static const char* weather[] = { "None", "Ash", "Snow", "Stars" };
                        ApraGui::SectionHeader("Weather & Removals");
                        ImGui::Combo("Weather", &Config::i(g_Variables.m_WorldEffects.m_iSelectedCustomWorldParticles), weather, IM_ARRAYSIZE(weather));
                        ImGui::SliderInt("Density", &Config::i(g_Variables.m_WorldEffects.m_iSelectedCustomWorldParticleDensity), 5, 200);
                        ApraGui::CheckBox("Remove Blur", &Config::b(g_Variables.m_WorldEffects.m_bRemoveBlur));
                        ApraGui::CheckBox("No Post-Process", &Config::b(g_Variables.m_WorldEffects.m_bDisablePostProcessing));
                    }
                    ImGui::CustomEndChild();
                }
                ImGui::EndGroup();

                ImGui::SameLine();

                ImGui::BeginGroup();
                {
                    ImGui::CustomBeginChild("GRENADES", ImVec2(group_width, grenades_h));
                    {
                        ApraGui::SectionHeader("Grenade Visuals");
                        Color& modelColor = Config::c(g_Variables.m_WorldVisuals.m_cGrenadePrediction);
                        float colorArray[4] = { modelColor.rBase(), modelColor.gBase(), modelColor.bBase(), modelColor.aBase() };
                        ImGui::Pickerbox("Prediction", &Config::b(g_Variables.m_WorldVisuals.m_bGrenadePrediction), colorArray, picker_flags);
                        modelColor.Set(colorArray[0], colorArray[1], colorArray[2], colorArray[3]);

                        Color& modelColor1 = Config::c(g_Variables.m_WorldVisuals.m_colInferno);
                        float colorArray1[4] = { modelColor1.rBase(), modelColor1.gBase(), modelColor1.bBase(), modelColor1.aBase() };
                        ImGui::Pickerbox("Molotov Area", &Config::b(g_Variables.m_WorldVisuals.m_bDrawInferno), colorArray1, picker_flags);
                        modelColor1.Set(colorArray1[0], colorArray1[1], colorArray1[2], colorArray1[3]);

                        Color& proximityColor = Config::c(g_Variables.m_WorldVisuals.m_cGrenadeProximityWraning);
                        float proximityArr[4] = { proximityColor.rBase(), proximityColor.gBase(), proximityColor.bBase(), proximityColor.aBase() };
                        ImGui::Pickerbox("Proximity", &Config::b(g_Variables.m_WorldVisuals.m_bGrenadeProximityWarning), proximityArr, picker_flags);
                        proximityColor.Set(proximityArr[0], proximityArr[1], proximityArr[2], proximityArr[3]);
                    }
                    ImGui::CustomEndChild();

                    ImGui::CustomBeginChild("TRACERS", ImVec2(group_width, tracers_h));
                    {
                        ApraGui::SectionHeader("Bullet Tracers");
                        Color& localColor = Config::c(g_Variables.m_WorldEffects.m_colLocalBulletTracers);
                        float localArr[4] = { localColor.rBase(), localColor.gBase(), localColor.bBase(), localColor.aBase() };
                        ImGui::Pickerbox("Local", &Config::b(g_Variables.m_WorldEffects.m_bLocalBulletTracers), localArr, picker_flags);
                        localColor.Set(localArr[0], localArr[1], localArr[2], localArr[3]);

                        Color& enemyColor = Config::c(g_Variables.m_WorldEffects.m_colEnemyBulletTracers);
                        float enemyArr[4] = { enemyColor.rBase(), enemyColor.gBase(), enemyColor.bBase(), enemyColor.aBase() };
                        ImGui::Pickerbox("Enemy", &Config::b(g_Variables.m_WorldEffects.m_bEnemyBulletTracers), enemyArr, picker_flags);
                        enemyColor.Set(enemyArr[0], enemyArr[1], enemyArr[2], enemyArr[3]);

                        Color& teamColor = Config::c(g_Variables.m_WorldEffects.m_colTeamBulletTracers);
                        float teamArr[4] = { teamColor.rBase(), teamColor.gBase(), teamColor.bBase(), teamColor.aBase() };
                        ImGui::Pickerbox("Team", &Config::b(g_Variables.m_WorldEffects.m_bTeamBulletTracers), teamArr, picker_flags);
                        teamColor.Set(teamArr[0], teamArr[1], teamArr[2], teamArr[3]);

                        Color& impactColor = Config::c(g_Variables.m_WorldEffects.m_colDrawImpactsColor);
                        float impactArr[4] = { impactColor.rBase(), impactColor.gBase(), impactColor.bBase(), impactColor.aBase() };
                        ImGui::Pickerbox("Impacts", &Config::b(g_Variables.m_WorldEffects.m_bDrawImpacts), impactArr, picker_flags);
                        impactColor.Set(impactArr[0], impactArr[1], impactArr[2], impactArr[3]);
                    }
                    ImGui::CustomEndChild();

                    ImGui::CustomBeginChild("HIT FEEDBACK", ImVec2(group_width, feedback_h));
                    {
                        ApraGui::SectionHeader("Hit Markers");
                        Color& modelColor = Config::c(g_Variables.m_Visuals.m_colHitmarker);
                        float colorArray[4] = { modelColor.rBase(), modelColor.gBase(), modelColor.bBase(), modelColor.aBase() };
                        ImGui::Pickerbox("World", &Config::b(g_Variables.m_Visuals.m_bHitmarker), colorArray, picker_flags);
                        modelColor.Set(colorArray[0], colorArray[1], colorArray[2], colorArray[3]);

                        Color& modelColor2 = Config::c(g_Variables.m_Misc.m_colHitMarker);
                        float colorArray2[4] = { modelColor2.rBase(), modelColor2.gBase(), modelColor2.bBase(), modelColor2.aBase() };
                        ImGui::Pickerbox("2D", &Config::b(g_Variables.m_Misc.m_bHitMarker), colorArray2, picker_flags);
                        modelColor2.Set(colorArray2[0], colorArray2[1], colorArray2[2], colorArray2[3]);
                    }
                    ImGui::CustomEndChild();
                }
                ImGui::EndGroup();
            }
            // VISUALS - WORLD SUBTAB (ESP)
            else if (visuals_subtabs == 1) {
                ImGui::BeginGroup();
                {
                    // ENEMY ESP Section
                        ImGui::CustomBeginChild("ENEMY ESP", ImVec2(group_width, group_height_full));
                        {
                            ApraGui::SectionHeader("Player Info");
                            Color& modelColor = Config::c(g_Variables.m_Visuals.m_colNameEsp);
                            float colorArray[4] = { modelColor.rBase(), modelColor.gBase(), modelColor.bBase(), modelColor.aBase() };
                            ImGui::Pickerbox("Name", &Config::b(g_Variables.m_Visuals.m_bEnableNameESP), colorArray, picker_flags);
                            modelColor.Set(colorArray[0], colorArray[1], colorArray[2], colorArray[3]);

                            Color& modelColor1 = Config::c(g_Variables.m_Visuals.m_colBoxESP);
                            float colorArray1[4] = { modelColor1.rBase(), modelColor1.gBase(), modelColor1.bBase(), modelColor1.aBase() };
                            ImGui::Pickerbox("Box", &Config::b(g_Variables.m_Visuals.m_bEnableBoxESP), colorArray1, picker_flags);
                            modelColor1.Set(colorArray1[0], colorArray1[1], colorArray1[2], colorArray1[3]);

                            Color& modelColor2 = Config::c(g_Variables.m_Visuals.m_colHealthBarEsp);
                            float colorArray2[4] = { modelColor2.rBase(), modelColor2.gBase(), modelColor2.bBase(), modelColor2.aBase() };
                            ImGui::Pickerbox("Health", &Config::b(g_Variables.m_Visuals.m_bEnableHealthESP), colorArray2, picker_flags);
                            modelColor2.Set(colorArray2[0], colorArray2[1], colorArray2[2], colorArray2[3]);

                            Color& modelColor3 = Config::c(g_Variables.m_Visuals.m_colWeaponEsp);
                            float colorArray3[4] = { modelColor3.rBase(), modelColor3.gBase(), modelColor3.bBase(), modelColor3.aBase() };
                            ImGui::Pickerbox("Weapon", &Config::b(g_Variables.m_Visuals.m_bEnableWeaponESP), colorArray3, picker_flags);
                            modelColor3.Set(colorArray3[0], colorArray3[1], colorArray3[2], colorArray3[3]);

                            Color& modelColor4 = Config::c(g_Variables.m_Visuals.m_colWeaponIcon);
                            float colorArray4[4] = { modelColor4.rBase(), modelColor4.gBase(), modelColor4.bBase(), modelColor4.aBase() };
                            ImGui::Pickerbox("Weapon Icon", &Config::b(g_Variables.m_Visuals.m_bEnableWeaponIconESP), colorArray4, picker_flags);
                            modelColor4.Set(colorArray4[0], colorArray4[1], colorArray4[2], colorArray4[3]);

                            ApraGui::SectionHeader("Rendering");
                            Color& modelColor5 = Config::c(g_Variables.m_Visuals.m_colSkeletonEsp);
                            float colorArray5[4] = { modelColor5.rBase(), modelColor5.gBase(), modelColor5.bBase(), modelColor5.aBase() };
                            ImGui::Pickerbox("Skeleton", &Config::b(g_Variables.m_Visuals.m_bEnableSkeletonESP), colorArray5, picker_flags);
                            modelColor5.Set(colorArray5[0], colorArray5[1], colorArray5[2], colorArray5[3]);

                            Color& modelColor7 = Config::c(g_Variables.m_Visuals.m_colGlow);
                            float colorArray7[4] = { modelColor7.rBase(), modelColor7.gBase(), modelColor7.bBase(), modelColor7.aBase() };
                            ImGui::Pickerbox("Glow", &Config::b(g_Variables.m_Visuals.m_bGlow), colorArray7, picker_flags);
                            modelColor7.Set(colorArray7[0], colorArray7[1], colorArray7[2], colorArray7[3]);

                            ApraGui::SectionHeader("Indicators");
                            Color& modelColor6 = Config::c(g_Variables.m_Visuals.m_colOOFArrows);
                            float colorArray6[4] = { modelColor6.rBase(), modelColor6.gBase(), modelColor6.bBase(), modelColor6.aBase() };
                            ImGui::Pickerbox("OOF Arrows", &Config::b(g_Variables.m_Visuals.m_bEnableOOFArrows), colorArray6, picker_flags);
                            modelColor6.Set(colorArray6[0], colorArray6[1], colorArray6[2], colorArray6[3]);

                            Color& histSkeletonColor = Config::c(g_Variables.m_Visuals.m_colHistorySkeleton);
                            float histSkeletonArr[4] = { histSkeletonColor.rBase(), histSkeletonColor.gBase(), histSkeletonColor.bBase(), histSkeletonColor.aBase() };
                            ImGui::Pickerbox("Backtrack", &Config::b(g_Variables.m_Visuals.m_bHistorySkeleton), histSkeletonArr, picker_flags);
                            histSkeletonColor.Set(histSkeletonArr[0], histSkeletonArr[1], histSkeletonArr[2], histSkeletonArr[3]);

                            Color& soundEspColor = Config::c(g_Variables.m_Visuals.m_colSoundESP);
                            float soundEspArr[4] = { soundEspColor.rBase(), soundEspColor.gBase(), soundEspColor.bBase(), soundEspColor.aBase() };
                            ImGui::Pickerbox("Sound ESP", &Config::b(g_Variables.m_Visuals.m_bSoundESP), soundEspArr, picker_flags);
                            soundEspColor.Set(soundEspArr[0], soundEspArr[1], soundEspArr[2], soundEspArr[3]);

                            Color& modelColorSnaplines = Config::c(g_Variables.m_Visuals.m_colSnaplines);
                            float colorArraySnaplines[4] = { modelColorSnaplines.rBase(), modelColorSnaplines.gBase(), modelColorSnaplines.bBase(), modelColorSnaplines.aBase() };
                            ImGui::Pickerbox("Snaplines", &Config::b(g_Variables.m_Visuals.m_bEnableSnaplines), colorArraySnaplines, picker_flags);
                            modelColorSnaplines.Set(colorArraySnaplines[0], colorArraySnaplines[1], colorArraySnaplines[2], colorArraySnaplines[3]);

                            if (Config::b(g_Variables.m_Visuals.m_bEnableSnaplines)) {
                                static const char* snaplinePositions[] = { "Bottom", "Top", "Center" };
                                ImGui::Combo("Position##snap", &Config::i(g_Variables.m_Visuals.m_iSnaplinePosition), snaplinePositions, IM_ARRAYSIZE(snaplinePositions));
                            }

                            static const char* flags[] = { "Money", "Armor", "Kit", "Scoped", "Defusing", "Latency" };
                            ImGui::MultiCombo("Flags", Config::vb(g_Variables.m_Visuals.m_vecFlags), flags, IM_ARRAYSIZE(flags));
                        }
                        ImGui::CustomEndChild();
                }
                ImGui::EndGroup();

                ImGui::SameLine();

                ImGui::BeginGroup();
                {
                    ImGui::CustomBeginChild("TEAM ESP", ImVec2(group_width, group_height_full));
                    {
                        ApraGui::SectionHeader("Player Info");
                        Color& teamNameColor = Config::c(g_Variables.m_Visuals.m_colTeamNameEsp);
                        float teamNameArr[4] = { teamNameColor.rBase(), teamNameColor.gBase(), teamNameColor.bBase(), teamNameColor.aBase() };
                        ImGui::Pickerbox("Name##team", &Config::b(g_Variables.m_Visuals.m_bEnableTeamNameESP), teamNameArr, picker_flags);
                        teamNameColor.Set(teamNameArr[0], teamNameArr[1], teamNameArr[2], teamNameArr[3]);

                        Color& teamBoxColor = Config::c(g_Variables.m_Visuals.m_colTeamBoxESP);
                        float teamBoxArr[4] = { teamBoxColor.rBase(), teamBoxColor.gBase(), teamBoxColor.bBase(), teamBoxColor.aBase() };
                        ImGui::Pickerbox("Box##team", &Config::b(g_Variables.m_Visuals.m_bEnableTeamBoxESP), teamBoxArr, picker_flags);
                        teamBoxColor.Set(teamBoxArr[0], teamBoxArr[1], teamBoxArr[2], teamBoxArr[3]);

                        Color& teamHealthColor = Config::c(g_Variables.m_Visuals.m_colTeamHealthBarEsp);
                        float teamHealthArr[4] = { teamHealthColor.rBase(), teamHealthColor.gBase(), teamHealthColor.bBase(), teamHealthColor.aBase() };
                        ImGui::Pickerbox("Health##team", &Config::b(g_Variables.m_Visuals.m_bEnableTeamHealthESP), teamHealthArr, picker_flags);
                        teamHealthColor.Set(teamHealthArr[0], teamHealthArr[1], teamHealthArr[2], teamHealthArr[3]);

                        Color& teamWeaponColor = Config::c(g_Variables.m_Visuals.m_colTeamWeaponEsp);
                        float teamWeaponArr[4] = { teamWeaponColor.rBase(), teamWeaponColor.gBase(), teamWeaponColor.bBase(), teamWeaponColor.aBase() };
                        ImGui::Pickerbox("Weapon##team", &Config::b(g_Variables.m_Visuals.m_bEnableTeamWeaponESP), teamWeaponArr, picker_flags);
                        teamWeaponColor.Set(teamWeaponArr[0], teamWeaponArr[1], teamWeaponArr[2], teamWeaponArr[3]);

                        Color& teamWeaponIconColor = Config::c(g_Variables.m_Visuals.m_colTeamWeaponIcon);
                        float teamWeaponIconArr[4] = { teamWeaponIconColor.rBase(), teamWeaponIconColor.gBase(), teamWeaponIconColor.bBase(), teamWeaponIconColor.aBase() };
                        ImGui::Pickerbox("Weapon Icon##team", &Config::b(g_Variables.m_Visuals.m_bEnableTeamWeaponIconESP), teamWeaponIconArr, picker_flags);
                        teamWeaponIconColor.Set(teamWeaponIconArr[0], teamWeaponIconArr[1], teamWeaponIconArr[2], teamWeaponIconArr[3]);

                        ApraGui::SectionHeader("Rendering");
                        Color& teamSkeletonColor = Config::c(g_Variables.m_Visuals.m_colTeamSkeletonEsp);
                        float teamSkeletonArr[4] = { teamSkeletonColor.rBase(), teamSkeletonColor.gBase(), teamSkeletonColor.bBase(), teamSkeletonColor.aBase() };
                        ImGui::Pickerbox("Skeleton##team", &Config::b(g_Variables.m_Visuals.m_bEnableTeamSkeletonESP), teamSkeletonArr, picker_flags);
                        teamSkeletonColor.Set(teamSkeletonArr[0], teamSkeletonArr[1], teamSkeletonArr[2], teamSkeletonArr[3]);

                        Color& teamGlowColor = Config::c(g_Variables.m_Visuals.m_colTeamGlow);
                        float teamGlowArr[4] = { teamGlowColor.rBase(), teamGlowColor.gBase(), teamGlowColor.bBase(), teamGlowColor.aBase() };
                        ImGui::Pickerbox("Glow##team", &Config::b(g_Variables.m_Visuals.m_bTeamGlow), teamGlowArr, picker_flags);teamGlowColor.Set(teamGlowArr[0], teamGlowArr[1], teamGlowArr[2], teamGlowArr[3]);

                        ApraGui::SectionHeader("Indicators");
                        Color& teamOOFColor = Config::c(g_Variables.m_Visuals.m_colTeamOOFArrows);
                        float teamOOFArr[4] = { teamOOFColor.rBase(), teamOOFColor.gBase(), teamOOFColor.bBase(), teamOOFColor.aBase() };
                        ImGui::Pickerbox("OOF Arrows##team", &Config::b(g_Variables.m_Visuals.m_bEnableTeamOOFArrows), teamOOFArr, picker_flags);
                        teamOOFColor.Set(teamOOFArr[0], teamOOFArr[1], teamOOFArr[2], teamOOFArr[3]);

                        Color& teamSnapColor = Config::c(g_Variables.m_Visuals.m_colTeamSnaplines);
                        float teamSnapArr[4] = { teamSnapColor.rBase(), teamSnapColor.gBase(), teamSnapColor.bBase(), teamSnapColor.aBase() };
                        ImGui::Pickerbox("Snaplines##team", &Config::b(g_Variables.m_Visuals.m_bEnableTeamSnaplines), teamSnapArr, picker_flags);
                        teamSnapColor.Set(teamSnapArr[0], teamSnapArr[1], teamSnapArr[2], teamSnapArr[3]);

                        if (Config::b(g_Variables.m_Visuals.m_bEnableTeamSnaplines)) {
                            static const char* snaplinePositions[] = { "Bottom", "Top", "Center" };
                            ImGui::Combo("Position##teamsnap", &Config::i(g_Variables.m_Visuals.m_iTeamSnaplinePosition), snaplinePositions, IM_ARRAYSIZE(snaplinePositions));
                        }

                        static const char* teamFlags[] = { "Money", "Armor", "Kit", "Scoped", "Defusing", "Latency" };
                        ImGui::MultiCombo("Flags##team", Config::vb(g_Variables.m_Visuals.m_vecTeamFlags), teamFlags, IM_ARRAYSIZE(teamFlags));
                    }
                    ImGui::CustomEndChild();
                }
                ImGui::EndGroup();

            }
            // VISUALS - CHAMS SUBTAB
            else if (visuals_subtabs == 2) {
                // Calculate dynamic heights
                const float enemy_chams_h = 220.f;
                const float local_chams_h = group_height_full - enemy_chams_h - 12.f;
                const float team_chams_h = group_height_full;

                ImGui::BeginGroup();
                {
                    ImGui::CustomBeginChild("ENEMY CHAMS", ImVec2(group_width, enemy_chams_h));
                    {
                        ApraGui::SectionHeader("Enemy Players");
                        Color& modelColor = Config::c(g_Variables.m_Visuals.m_colEnemyChams);
                        float colorArray[4] = { modelColor.rBase(), modelColor.gBase(), modelColor.bBase(), modelColor.aBase() };

                        ImGui::Pickerbox("Enable Visible Chams", &Config::b(g_Variables.m_Visuals.m_bEnableEnemyChams), colorArray, picker_flags);

                        modelColor.Set(colorArray[0], colorArray[1], colorArray[2], colorArray[3]);

                        static const char* materials_visible[] = { "Latex", "Bloom", "Glow", "Solid", "Ghost" };

                        ImGui::Combo("Visible Chams Material", &Config::i(g_Variables.m_Visuals.m_iChamMaterialEnemy), materials_visible, IM_ARRAYSIZE(materials_visible));

                        Color& modelColor1 = Config::c(g_Variables.m_Visuals.m_colEnemyChamsIgnoreZ);
                        float colorArray1[4] = { modelColor1.rBase(), modelColor1.gBase(), modelColor1.bBase(), modelColor1.aBase() };
                        ImGui::Pickerbox("Enable Invisible Chams", &Config::b(g_Variables.m_Visuals.m_bEnableEnemyChamsIgnoreZ), colorArray1, picker_flags);
                        if (ImGui::IsItemHovered()) ImGui::SetTooltip("Show chams through walls");
                        modelColor1.Set(colorArray1[0], colorArray1[1], colorArray1[2], colorArray1[3]);

                        ImGui::Combo("Invisible Chams Material", &Config::i(g_Variables.m_Visuals.m_iChamMaterialEnemyIgnoreZ), materials_visible, IM_ARRAYSIZE(materials_visible));
                    }
                    ImGui::CustomEndChild();

                    ImGui::CustomBeginChild("LOCAL CHAMS", ImVec2(group_width, local_chams_h));
                    {
                        ApraGui::SectionHeader("Local Player");
                        Color& modelColor2 = Config::c(g_Variables.m_Visuals.m_colSelfChams);
                        float colorArray2[4] = { modelColor2.rBase(), modelColor2.gBase(), modelColor2.bBase(), modelColor2.aBase() };
                        ImGui::Pickerbox("Enable Local Chams", &Config::b(g_Variables.m_Visuals.m_bEnableSelfChams), colorArray2, picker_flags);
                        if (ImGui::IsItemHovered()) ImGui::SetTooltip("Apply chams to your player model");
                        modelColor2.Set(colorArray2[0], colorArray2[1], colorArray2[2], colorArray2[3]);

                        static const char* materials_local[] = { "Latex", "Bloom", "Glow", "Solid", "Ghost" };

                        ImGui::Combo("Local Chams Material", &Config::i(g_Variables.m_Visuals.m_iChamMaterialLocal), materials_local, IM_ARRAYSIZE(materials_local));

                        ImGui::Spacing();
                        ApraGui::SectionHeader("Fake Chams");

                        Color& fakeChamsColor = Config::c(g_Variables.m_Visuals.m_colFakeChams);
                        float fakeChamsArr[4] = { fakeChamsColor.rBase(), fakeChamsColor.gBase(), fakeChamsColor.bBase(), fakeChamsColor.aBase() };
                        ImGui::Pickerbox("Enable Fake Chams", &Config::b(g_Variables.m_Visuals.m_bEnableFakeChams), fakeChamsArr, picker_flags);
                        if (ImGui::IsItemHovered()) ImGui::SetTooltip("Show fake angle chams");
                        fakeChamsColor.Set(fakeChamsArr[0], fakeChamsArr[1], fakeChamsArr[2], fakeChamsArr[3]);

                        ImGui::BeginDisabled(!Config::b(g_Variables.m_Visuals.m_bEnableFakeChams));
                        {
                            Color& fakeChamsColor2 = Config::c(g_Variables.m_Visuals.m_colFakeChams2);
                            float fakeChamsArr2[4] = { fakeChamsColor2.rBase(), fakeChamsColor2.gBase(), fakeChamsColor2.bBase(), fakeChamsColor2.aBase() };
                            if (ImGui::ColorEdit4("Secondary Color##fake", fakeChamsArr2, picker_flags))
                                fakeChamsColor2.Set(fakeChamsArr2[0], fakeChamsArr2[1], fakeChamsArr2[2], fakeChamsArr2[3]);
                            ImGui::Combo("Fake Chams Material", &Config::i(g_Variables.m_Visuals.m_iFakeChamMaterial), materials_local, IM_ARRAYSIZE(materials_local));
                                                        ApraGui::CheckBox("Interpolate", &Config::b(g_Variables.m_Visuals.m_bFakeChamsInterpolate));
                                                        ApraGui::CheckBox("Pulsate", &Config::b(g_Variables.m_Visuals.m_bFakeChamsPulsate));
                        }
                        ImGui::EndDisabled();

                        ImGui::Spacing();
                        ApraGui::CheckBox("Hide Sleeves", &Config::b(g_Variables.m_Visuals.m_bHideSleeves));
                    }
                    ImGui::CustomEndChild();
                }
                ImGui::EndGroup();

                ImGui::SameLine();

                ImGui::BeginGroup();
                {
                    ImGui::CustomBeginChild("TEAM CHAMS", ImVec2(group_width, team_chams_h));
                    {
                        ApraGui::SectionHeader("Team Players");
                        Color& teamChamsColor = Config::c(g_Variables.m_Visuals.m_colTeamChams);
                        float teamChamsArr[4] = { teamChamsColor.rBase(), teamChamsColor.gBase(), teamChamsColor.bBase(), teamChamsColor.aBase() };
                        ImGui::Pickerbox("Enable Visible Chams##team", &Config::b(g_Variables.m_Visuals.m_bEnableTeamChams), teamChamsArr, picker_flags);
                        if (ImGui::IsItemHovered()) ImGui::SetTooltip("Apply chams to teammates");
                        teamChamsColor.Set(teamChamsArr[0], teamChamsArr[1], teamChamsArr[2], teamChamsArr[3]);

                        static const char* materials_team[] = { "Latex", "Bloom", "Glow", "Solid", "Ghost" };

                        ImGui::Combo("Visible Chams Material##team", &Config::i(g_Variables.m_Visuals.m_iChamMaterialTeam), materials_team, IM_ARRAYSIZE(materials_team));

                        Color& teamChamsIgnoreZColor = Config::c(g_Variables.m_Visuals.m_colTeamChamsIgnoreZ);
                        float teamChamsIgnoreZArr[4] = { teamChamsIgnoreZColor.rBase(), teamChamsIgnoreZColor.gBase(), teamChamsIgnoreZColor.bBase(), teamChamsIgnoreZColor.aBase() };
                        ImGui::Pickerbox("Enable Invisible Chams##team", &Config::b(g_Variables.m_Visuals.m_bEnableTeamChamsIgnoreZ), teamChamsIgnoreZArr, picker_flags);
                        if (ImGui::IsItemHovered()) ImGui::SetTooltip("Show teammate chams through walls");
                        teamChamsIgnoreZColor.Set(teamChamsIgnoreZArr[0], teamChamsIgnoreZArr[1], teamChamsIgnoreZArr[2], teamChamsIgnoreZArr[3]);

                        ImGui::Combo("Invisible Chams Material##team", &Config::i(g_Variables.m_Visuals.m_iChamMaterialTeamIgnoreZ), materials_team, IM_ARRAYSIZE(materials_team));
                    }
                    ImGui::CustomEndChild();
                }
                ImGui::EndGroup();
            }
            // VISUALS - EFFECTS SUBTAB
            else if (visuals_subtabs == 3) {
                // Calculate dynamic heights
                const float general_h = group_height_full;

                ImGui::BeginGroup();
                {
                    ImGui::CustomBeginChild("REMOVALS", ImVec2(group_width, general_h));
                    {
                        ApraGui::SectionHeader("Removals");
                        static const char* scope[] = { "Default", "Remove overlay", "Gradient", "Remove all" };
                        static const char* recoil[] = { "Default", "Remove punch", "Remove all" };
                        static const char* removals[] = { "Smoke", "Flash", "Torso", "Crosshair", "Overhead local", "Overhead team", "Team intro", "Fog brightness", "Scope zoom" };

                        ImGui::MultiCombo("Removals", Config::vb(g_Variables.m_WorldVisuals.m_vbRemovals), removals, IM_ARRAYSIZE(removals));
                        if (ImGui::IsItemHovered()) ImGui::SetTooltip("Select visual elements to remove");

                        ImGui::Combo("Remove Recoil", &Config::i(g_Variables.m_Visuals.m_iRemoveRecoil), recoil, IM_ARRAYSIZE(recoil));
                        ImGui::Combo("Remove Scope", &Config::i(g_Variables.m_Visuals.m_iRemoveScope), scope, IM_ARRAYSIZE(scope));

                        if (Config::vb(g_Variables.m_WorldVisuals.m_vbRemovals)[REMOVE_ZOOM]) {
                            ApraGui::CheckBox(X("Force Second Zoom"), &Config::b(g_Variables.m_Visuals.m_bForceZoom));
                        }

                        ImGui::Spacing();
                        ApraGui::SectionHeader("FOV & Aspect");
                        ImGui::SliderInt(X("Override FOV"), &Config::i(g_Variables.m_Visuals.m_iWorldFov), 50, 140);
                        if (ImGui::IsItemHovered()) ImGui::SetTooltip("Override game field of view");

                        ImGui::SliderInt(X("Override Aspect Ratio"), &Config::i(g_Variables.m_WorldEffects.m_iAspectRatio), 0, 50);
                        if (ImGui::IsItemHovered()) ImGui::SetTooltip("Stretch or compress screen aspect");

                        if (Config::i(g_Variables.m_Visuals.m_iRemoveScope) == SCOPE_GRADIENT) {
                            ImGui::Spacing();
                            ApraGui::SectionHeader("Scope Gradient");
                            Color& modelColor = Config::c(g_Variables.m_Visuals.m_colScope);
                            float colorArray[4] = {
                                modelColor.rBase(),
                                modelColor.gBase(),
                                modelColor.bBase(),
                                modelColor.aBase()
                            };

                            if (ImGui::ColorEdit4(X("Color-in"), colorArray, picker_flags))
                                modelColor.Set(colorArray[0], colorArray[1], colorArray[2], colorArray[3]);

                            Color& modelColor1 = Config::c(g_Variables.m_Visuals.m_colScopeOutSide);
                            float colorArray1[4] = {
                                modelColor1.rBase(),
                                modelColor1.gBase(),
                                modelColor1.bBase(),
                                modelColor1.aBase()
                            };

                            if (ImGui::ColorEdit4(X("Color-out"), colorArray1, picker_flags))
                                modelColor1.Set(colorArray1[0], colorArray1[1], colorArray1[2], colorArray1[3]);

                            ImGui::SliderInt(X("Scope Length"), &Config::i(g_Variables.m_Visuals.m_iScopeLength), 1, 500);
                            //ImGui::SliderInt(X("Scope Thickness"), &Config::i(g_Variables.m_Visuals.m_iScopeThickness), 1, 50);
                            ImGui::SliderInt(X("Scope Gap"), &Config::i(g_Variables.m_Visuals.m_iScopeGap), 0, 100);
                        }
                    }
                    ImGui::CustomEndChild();


                }
                ImGui::EndGroup();

                ImGui::SameLine();

                ImGui::BeginGroup();
                {
                    ImGui::CustomBeginChild("VIEWMODEL", ImVec2(group_width, 250.f));
                    {
                        ApraGui::SectionHeader("Viewmodel Settings");
                        ApraGui::ToggleSwitch("Override Viewmodel", &Config::b(g_Variables.m_Visuals.m_bViewmodelChanger));
                        if (ImGui::IsItemHovered()) ImGui::SetTooltip("Customize weapon viewmodel position");
                        ImGui::BeginDisabled(!Config::b(g_Variables.m_Visuals.m_bViewmodelChanger));
                        {
                            ImGui::SliderInt(X("Viewmodel FOV"), &Config::i(g_Variables.m_Visuals.iViewmodelFov), 40, 120);
                            ImGui::SliderFloat(X("Offset X"), &Config::f(g_Variables.m_Visuals.flXOffset), -90.f, 90.f, "%.1f");
                            ImGui::SliderFloat(X("Offset Y"), &Config::f(g_Variables.m_Visuals.flYOffset), -90.f, 90.f, "%.1f");
                            ImGui::SliderFloat(X("Offset Z"), &Config::f(g_Variables.m_Visuals.flZOffset), -90.f, 90.f, "%.1f");
                        }
                        ImGui::EndDisabled();}
                    ImGui::CustomEndChild();

                    ImGui::CustomBeginChild("THIRDPERSON", ImVec2(group_width, group_height_full - 250.f - 12.f));
                    {
                        ApraGui::SectionHeader("Thirdperson Settings");
                        ApraGui::ToggleSwitch("Enable Thirdperson", &Config::b(g_Variables.m_Visuals.m_bEnableThirdPerson));
                        if (ImGui::IsItemHovered()) ImGui::SetTooltip("Toggle thirdperson camera view");
                        ImGui::BeginDisabled(!Config::b(g_Variables.m_Visuals.m_bEnableThirdPerson));
                        {
                            ImGui::Keybind("Thirdperson Key", (int*)&Config::kb(g_Variables.m_Visuals.m_iThirdPersonKeybind).m_iKey, (int*)&Config::kb(g_Variables.m_Visuals.m_iThirdPersonKeybind).m_iMode, true);
                            ImGui::SliderInt("Distance", &Config::i(g_Variables.m_Visuals.m_iThirdPersonDistance), 30, 250);
                            ApraGui::CheckBox("Force When Dead", &Config::b(g_Variables.m_Visuals.m_bThirdPersonForceWhenDead));
                            ApraGui::CheckBox("Disable On Grenade", &Config::b(g_Variables.m_Visuals.m_bThirdPersonDisableOnGrenade));
                            ApraGui::CheckBox("Disable Animation", &Config::b(g_Variables.m_Visuals.m_bThirdPersonDisableAnimation));
                            ImGui::SliderInt("Transparency", &Config::i(g_Variables.m_Visuals.m_iThirdPersonTransparency), 0, 100, "%d%%");
                            ImGui::SliderInt("Transparency (Nades)", &Config::i(g_Variables.m_Visuals.m_iThirdPersonTransparencyNade), 0, 100, "%d%%");
                        }
                        ImGui::EndDisabled();
                    }
                    ImGui::CustomEndChild();
                }
                ImGui::EndGroup();
            }
        }
        else if (tabs == 2) {
            // MOVEMENT SUBTAB - Reorganized for better UX
            if (misc_subtabs == 0) {
                // Calculate dynamic heights - optimized for content
                const float move_panel_h = 340.f;
                const float peek_panel_h = group_height_full - move_panel_h - 12.f;
                const float autobuy_panel_h = 200.f;
                const float velocity_panel_h = group_height_full - autobuy_panel_h - 12.f;

                ImGui::BeginGroup();
                {
                    ImGui::CustomBeginChild("MOVEMENT", ImVec2(group_width, move_panel_h));
                    {
                        ApraGui::SectionHeader("Basic Movement");
                        ApraGui::ToggleSwitch("Bunny Hop", &Config::b(g_Variables.m_Movement.m_bEnableBunnyHop));
                        if (ImGui::IsItemHovered()) ImGui::SetTooltip("Automatically jump when landing");
                        ApraGui::ToggleSwitch("Air Strafe", &Config::b(g_Variables.m_Movement.m_bEnableAutoStrafe));
                        if (ImGui::IsItemHovered()) ImGui::SetTooltip("Automatically strafe in air for speed");
                        ImGui::BeginDisabled(!Config::b(g_Variables.m_Movement.m_bEnableAutoStrafe));
                        {
                            ImGui::SliderInt("Smoothing", &Config::i(g_Variables.m_Movement.m_iAutostrafeSmoothing), 0, 100, "%d%%");
                            ApraGui::CheckBox("Avoid Walls", &Config::b(g_Variables.m_Movement.m_bAvoidWalls));
                        }
                        ImGui::EndDisabled();
                        
                        ImGui::Spacing();
                        ImGui::Separator();
                        ImGui::Spacing();
                        ApraGui::SectionHeader("Advanced Movement");
                        ApraGui::ToggleSwitch("Standalone Quick Stop", &Config::b(g_Variables.m_Movement.m_bEnableQuickStop));
                        if (ImGui::IsItemHovered()) ImGui::SetTooltip("Quickly stop movement");
                        ImGui::Keybind("Edge Jump", (int*)&Config::kb(g_Variables.m_Movement.m_kEdgeJump).m_iKey, (int*)&Config::kb(g_Variables.m_Movement.m_kEdgeJump).m_iMode, true);
                        ApraGui::CheckBox("Instant Unduck", &Config::b(g_Variables.m_Movement.m_bInstantUnduck));
                        ApraGui::CheckBox("Slide", &Config::b(g_Variables.m_Movement.m_bSlide));
                        ApraGui::CheckBox("Stop In Air", &Config::b(g_Variables.m_Movement.m_bStopInAir));
                        ImGui::Keybind("Fake Duck", (int*)&Config::kb(g_Variables.m_Movement.m_kFakeDuck).m_iKey, (int*)&Config::kb(g_Variables.m_Movement.m_kFakeDuck).m_iMode, true);
                    }
                    ImGui::CustomEndChild();

                    ImGui::CustomBeginChild("PEEK ASSIST", ImVec2(group_width, peek_panel_h));
                    {
                        ApraGui::SectionHeader("Peek Settings");
                        Color& peekColor1 = Config::c(g_Variables.m_Movement.m_colPeekAssist);
                        float peekArr1[4] = { peekColor1.rBase(), peekColor1.gBase(), peekColor1.bBase(), peekColor1.aBase() };
                        
                        ImGui::Pickerbox("Enable Peek Assist", &Config::b(g_Variables.m_Movement.m_bPeekAssist), peekArr1, picker_flags);
                        peekColor1.Set(peekArr1[0], peekArr1[1], peekArr1[2], peekArr1[3]);
                        
                        ImGui::BeginDisabled(!Config::b(g_Variables.m_Movement.m_bPeekAssist));
                        {
                            Color& peekColor2 = Config::c(g_Variables.m_Movement.m_colPeekAssist2);
                            float peekArr2[4] = { peekColor2.rBase(), peekColor2.gBase(), peekColor2.bBase(), peekColor2.aBase() };
                            if (ImGui::ColorEdit4("Return Color", peekArr2, picker_flags))
                                peekColor2.Set(peekArr2[0], peekArr2[1], peekArr2[2], peekArr2[3]);
                            
                            ImGui::Keybind("Peek Key", (int*)&Config::kb(g_Variables.m_Movement.m_kPeekAssist).m_iKey, (int*)&Config::kb(g_Variables.m_Movement.m_kPeekAssist).m_iMode, true);
                            ApraGui::CheckBox("Retreat On Release", &Config::b(g_Variables.m_Movement.m_bPeekAssistRetreatOnRelease));
                        }
                        ImGui::EndDisabled();
                    }
                    ImGui::CustomEndChild();
                }
                ImGui::EndGroup();

                ImGui::SameLine();

                ImGui::BeginGroup();
                {
                    ImGui::CustomBeginChild("AUTOBUY", ImVec2(group_width, autobuy_panel_h));
                    {
                        ApraGui::SectionHeader("Auto Purchase");
                        ApraGui::ToggleSwitch("Enable Auto Purchase", &Config::b(g_Variables.m_Misc.m_bAutoPurchase));
                        ImGui::BeginDisabled(!Config::b(g_Variables.m_Misc.m_bAutoPurchase)); {
                            const char* buy1[] = { "None", "AK47/M4A1", "SG-556", "AWP", "SSG-08", "SCAR-20/G3SG1", "Negev" };
                            const char* buy2[] = { "None", "Deagle/Revolver", "Dual Berettas", "Tec-9/Five-SeveN", "P250" };
                            const char* buy3[] = { "Armor", "Helmet", "Taser", "Grenades", "Defuser" };

                            ImGui::Combo("Primary", &Config::i(g_Variables.m_Misc.m_iAutoPurchase1), buy1, IM_ARRAYSIZE(buy1));
                            ImGui::Combo("Secondary", &Config::i(g_Variables.m_Misc.m_iAutoPurchase2), buy2, IM_ARRAYSIZE(buy2));
                            ImGui::MultiCombo("Additional", Config::vb(g_Variables.m_Misc.m_uAutoPurchase3), buy3, IM_ARRAYSIZE(buy3));
                        }
                        ImGui::EndDisabled();
                    }
                    ImGui::CustomEndChild();

                    ImGui::CustomBeginChild("INDICATORS", ImVec2(group_width, velocity_panel_h));
                    {
                        ApraGui::SectionHeader("Movement Indicators");
                        ApraGui::ToggleSwitch("Velocity Indicator", &Config::b(g_Variables.m_Misc.m_bVelocityIndicator));
                        if (ImGui::IsItemHovered()) ImGui::SetTooltip("Show current movement speed");
                        ImGui::BeginDisabled(!Config::b(g_Variables.m_Misc.m_bVelocityIndicator));
                        {
                            float colVel[4] = { Config::c(g_Variables.m_Misc.m_colVelocityIndicator).rBase(), Config::c(g_Variables.m_Misc.m_colVelocityIndicator).gBase(), Config::c(g_Variables.m_Misc.m_colVelocityIndicator).bBase(), Config::c(g_Variables.m_Misc.m_colVelocityIndicator).aBase() };
                            if (ImGui::ColorEdit4("Color##VelocityIndicator", colVel, ImGuiColorEditFlags_NoInputs | ImGuiColorEditFlags_AlphaBar))
                                Config::c(g_Variables.m_Misc.m_colVelocityIndicator) = Color(colVel[0], colVel[1], colVel[2], colVel[3]);
                        }
                        ImGui::EndDisabled();
                    }
                    ImGui::CustomEndChild();
                }
                ImGui::EndGroup();
            }
            // HUD SUBTAB - Reorganized for better UX
            else if (misc_subtabs == 1) {
                // Calculate dynamic heights - optimized for content
                const float crosshair_h = 320.f;
                const float world_h = group_height_full - crosshair_h - 12.f;
                const float indicators_h = 220.f;
                const float overlay_h = group_height_full - indicators_h - 12.f;

                ImGui::BeginGroup();
                {
                    ImGui::CustomBeginChild("CROSSHAIR", ImVec2(group_width, crosshair_h));
                    {
                        ApraGui::SectionHeader("Crosshair Options");
                        ApraGui::ToggleSwitch("Custom Crosshair", &Config::b(g_Variables.m_Misc.m_bCustomCrosshair));
                        if (ImGui::IsItemHovered()) ImGui::SetTooltip("Enable custom crosshair overlay");
                        ImGui::BeginDisabled(!Config::b(g_Variables.m_Misc.m_bCustomCrosshair));
                        {
                            const char* crosshairStyles[] = { "Cross", "Circle", "X" };
                            ImGui::Combo("Style", &Config::i(g_Variables.m_Misc.m_iCrosshairStyle), crosshairStyles, IM_ARRAYSIZE(crosshairStyles));
                            ImGui::SliderInt("Size", &Config::i(g_Variables.m_Misc.m_iCrosshairSize), 1, 20);
                            ImGui::SliderInt("Gap", &Config::i(g_Variables.m_Misc.m_iCrosshairGap), 0, 10);
                            ImGui::SliderInt("Thickness", &Config::i(g_Variables.m_Misc.m_iCrosshairThickness), 1, 5);
                            ApraGui::CheckBox("Center Dot", &Config::b(g_Variables.m_Misc.m_bCrosshairDot));
                            float colCross[4] = { Config::c(g_Variables.m_Misc.m_colCustomCrosshair).rBase(), Config::c(g_Variables.m_Misc.m_colCustomCrosshair).gBase(), Config::c(g_Variables.m_Misc.m_colCustomCrosshair).bBase(), Config::c(g_Variables.m_Misc.m_colCustomCrosshair).aBase() };
                            if (ImGui::ColorEdit4("Color##CustomCrosshair", colCross, ImGuiColorEditFlags_NoInputs | ImGuiColorEditFlags_AlphaBar))
                                Config::c(g_Variables.m_Misc.m_colCustomCrosshair) = Color(colCross[0], colCross[1], colCross[2], colCross[3]);
                        }
                        ImGui::EndDisabled();
                        
                                                ApraGui::CheckBox("Recoil Crosshair", &Config::b(g_Variables.m_Misc.m_bRecoilCrosshair));
                        ImGui::BeginDisabled(!Config::b(g_Variables.m_Misc.m_bRecoilCrosshair));
                        {
                            float colRecoil[4] = { Config::c(g_Variables.m_Misc.m_colRecoilCrosshair).rBase(), Config::c(g_Variables.m_Misc.m_colRecoilCrosshair).gBase(), Config::c(g_Variables.m_Misc.m_colRecoilCrosshair).bBase(), Config::c(g_Variables.m_Misc.m_colRecoilCrosshair).aBase() };
                            if (ImGui::ColorEdit4("Crosshair Color", colRecoil, ImGuiColorEditFlags_NoInputs | ImGuiColorEditFlags_AlphaBar))
                                Config::c(g_Variables.m_Misc.m_colRecoilCrosshair) = Color(colRecoil[0], colRecoil[1], colRecoil[2], colRecoil[3]);
                        }
                        ImGui::EndDisabled();
                        
                        ApraGui::CheckBox("Spread Circle", &Config::b(g_Variables.m_Misc.m_bSpreadCircle));
                        ImGui::BeginDisabled(!Config::b(g_Variables.m_Misc.m_bSpreadCircle));
                        {
                            float colSpread[4] = { Config::c(g_Variables.m_Misc.m_colSpreadCircle).rBase(), Config::c(g_Variables.m_Misc.m_colSpreadCircle).gBase(), Config::c(g_Variables.m_Misc.m_colSpreadCircle).bBase(), Config::c(g_Variables.m_Misc.m_colSpreadCircle).aBase() };
                            if (ImGui::ColorEdit4("Color##SpreadCircle", colSpread, ImGuiColorEditFlags_NoInputs | ImGuiColorEditFlags_AlphaBar))
                                Config::c(g_Variables.m_Misc.m_colSpreadCircle) = Color(colSpread[0], colSpread[1], colSpread[2], colSpread[3]);
                        }
                        ImGui::EndDisabled();
                    }
                    ImGui::CustomEndChild();

                    ImGui::CustomBeginChild("WORLD", ImVec2(group_width, world_h));
                    {
                        ApraGui::SectionHeader("World Settings");
                        ImGui::Spacing();
                                ApraGui::CheckBox("Night Mode", &Config::b(g_Variables.m_Misc.m_bNightMode));
                        ImGui::BeginDisabled(!Config::b(g_Variables.m_Misc.m_bNightMode));
                        {
                            ImGui::SliderInt("Intensity", &Config::i(g_Variables.m_Misc.m_iNightModeIntensity), 10, 100, "%d%%");
                        }
                        ImGui::EndDisabled();
                        
                        ApraGui::CheckBox("World FOV", &Config::b(g_Variables.m_Misc.m_bWorldFOV));
                        ImGui::BeginDisabled(!Config::b(g_Variables.m_Misc.m_bWorldFOV));
                        {
                            ImGui::SliderInt("FOV", &Config::i(g_Variables.m_Misc.m_iWorldFOV), 60, 150);
                        }
                        ImGui::EndDisabled();
                        
                        ApraGui::CheckBox("Anti-OBS", &Config::b(g_Variables.m_Misc.m_bAntiOBS));
                    }
                    ImGui::CustomEndChild();
                }
                ImGui::EndGroup();

                ImGui::SameLine();

                ImGui::BeginGroup();
                {
                    ImGui::CustomBeginChild("INDICATORS", ImVec2(group_width, indicators_h));
                    {
                        ApraGui::SectionHeader("Game Indicators");
                        ImGui::Spacing();
                        ApraGui::CheckBox("Bomb Timer", &Config::b(g_Variables.m_Misc.m_bBombTimer));
                        ImGui::BeginDisabled(!Config::b(g_Variables.m_Misc.m_bBombTimer));
                        {
                            float colBomb[4] = { Config::c(g_Variables.m_Misc.m_colBombTimer).rBase(), Config::c(g_Variables.m_Misc.m_colBombTimer).gBase(), Config::c(g_Variables.m_Misc.m_colBombTimer).bBase(), Config::c(g_Variables.m_Misc.m_colBombTimer).aBase() };
                            if (ImGui::ColorEdit4("Color##BombTimer", colBomb, ImGuiColorEditFlags_NoInputs | ImGuiColorEditFlags_AlphaBar))
                                Config::c(g_Variables.m_Misc.m_colBombTimer) = Color(colBomb[0], colBomb[1], colBomb[2], colBomb[3]);
                        }
                        ImGui::EndDisabled();
                        
                        ApraGui::CheckBox("Flash Indicator", &Config::b(g_Variables.m_Misc.m_bFlashIndicator));
                        ImGui::BeginDisabled(!Config::b(g_Variables.m_Misc.m_bFlashIndicator));
                        {
                            float colFlash[4] = { Config::c(g_Variables.m_Misc.m_colFlashIndicator).rBase(), Config::c(g_Variables.m_Misc.m_colFlashIndicator).gBase(), Config::c(g_Variables.m_Misc.m_colFlashIndicator).bBase(), Config::c(g_Variables.m_Misc.m_colFlashIndicator).aBase() };
                            if (ImGui::ColorEdit4("Color##FlashIndicator", colFlash, ImGuiColorEditFlags_NoInputs | ImGuiColorEditFlags_AlphaBar))
                                Config::c(g_Variables.m_Misc.m_colFlashIndicator) = Color(colFlash[0], colFlash[1], colFlash[2], colFlash[3]);
                        }
                        ImGui::EndDisabled();
                        ApraGui::CheckBox("Wallbang Indicator", &Config::b(g_Variables.m_Gui.m_bWallbangIndicator));
                        ApraGui::CheckBox("Hit Logs", &Config::b(g_Variables.m_Misc.m_bHitlogs));
                    }
                    ImGui::CustomEndChild();

                    ImGui::CustomBeginChild("OVERLAY", ImVec2(group_width, overlay_h));
                    {
                        ApraGui::SectionHeader("Overlay");
                        const char* overlay[] = { "Indicators", "Spectators", "Watermark" };
                        ImGui::MultiCombo("Elements", Config::vb(g_Variables.m_Gui.m_vbOverlay), overlay, IM_ARRAYSIZE(overlay));

                        ApraGui::SectionHeader("Misc");
                        ApraGui::CheckBox("Bypass sv_pure", &Config::b(g_Variables.m_Misc.m_bBypassSvPure));
                        ApraGui::CheckBox("Reveal Ranks", &Config::b(g_Variables.m_Misc.m_bRevealRanks));
                        ApraGui::CheckBox("Hurt Log", &Config::b(g_Variables.m_Misc.m_bHurtLog));
                        ApraGui::CheckBox("Buy Log", &Config::b(g_Variables.m_Misc.m_bBuyLog));
                        ApraGui::CheckBox("Preserve Kills", &Config::b(g_Variables.m_Misc.m_bPreserveDeathnotices));
                        ApraGui::CheckBox("Clan Tag", &Config::b(g_Variables.m_Misc.m_bClanTag));
                        ApraGui::CheckBox("Party Mode", &Config::b(g_Variables.m_Misc.m_bPartyMode));
                        ImGui::SliderInt("Ragdoll", &Config::i(g_Variables.m_Misc.m_iRagdollThrust), 1, 1000, "%d%%");
                        ImGui::SliderInt("Ping Spike", &Config::i(g_Variables.m_Misc.m_iPingSpike), 0, 200, "%d ms");
                    }
                    ImGui::CustomEndChild();
                }
                ImGui::EndGroup();
            }
            // AUTOMATION SUBTAB - Reorganized layout for better UX
            else if (misc_subtabs == 2) {
                // Calculate dynamic heights based on content
                const float panel_spacing = 12.f;
                const float auto_panel_h = 230.f;  // 6 checkboxes + header
                const float sound_panel_h = group_height_full - auto_panel_h - panel_spacing;
                const float chat_panel_h = 280.f;  // Chat features
                const float extras_panel_h = group_height_full - chat_panel_h - panel_spacing;

                ImGui::BeginGroup();
                {
                    // AUTOMATION - Compact panel for automation features
                    ImGui::CustomBeginChild("AUTOMATION", ImVec2(group_width, auto_panel_h));
                    {
                        ApraGui::SectionHeader("Auto Actions");
                        ApraGui::CheckBox("Auto Pistol", &Config::b(g_Variables.m_Misc.m_bAutoPistol));
                        ApraGui::CheckBox("Auto Scope", &Config::b(g_Variables.m_Misc.m_bAutoScope));
                        ApraGui::CheckBox("Quick Plant", &Config::b(g_Variables.m_Misc.m_bQuickPlant));
                        ApraGui::CheckBox("Radar Hack", &Config::b(g_Variables.m_Misc.m_bRadarHack));
                        
                        ImGui::Spacing();
                        ApraGui::SectionHeader("Knife Bot");
                        ImGui::Spacing();
                        ApraGui::CheckBox("Enable Knife Bot", &Config::b(g_Variables.m_Misc.m_bKnifeBot));
                        ImGui::BeginDisabled(!Config::b(g_Variables.m_Misc.m_bKnifeBot));
                        {
                            ApraGui::CheckBox("Only Backstab", &Config::b(g_Variables.m_Misc.m_bKnifeBotBackstab));
                        }
                        ImGui::EndDisabled();
                    }
                    ImGui::CustomEndChild();

                    // SOUND - Hit feedback and audio settings
                    ImGui::CustomBeginChild("SOUND", ImVec2(group_width, sound_panel_h));
                    {
                        ApraGui::SectionHeader("Audio Effects");
                        ImGui::Spacing();
                        ApraGui::CheckBox("Distant Scout Sound", &Config::b(g_Variables.m_Misc.m_bDistantScoutSound));
                        
                        ImGui::Spacing();
                        ImGui::Separator();
                        ImGui::Spacing();
                        ApraGui::SectionHeader("Hit Feedback");
                        ImGui::Spacing();
                        ApraGui::CheckBox("Enable Hit Sound", &Config::b(g_Variables.m_Misc.m_bHitSound));
                        ImGui::BeginDisabled(!Config::b(g_Variables.m_Misc.m_bHitSound));
                        {
                            const char* hitsound[] = { "Metallic", "Light", "Coin", "Money", "Whack", "Bell", "CoD", "Fatality", "Arena", "Phonk", "Sparkle" };
                            ImGui::Combo("Sound Type", &Config::i(g_Variables.m_Misc.m_iHitSoundIndex), hitsound, IM_ARRAYSIZE(hitsound));
                            ImGui::SliderFloat("Volume", &Config::f(g_Variables.m_Misc.m_flHitsoundVolume), 0.f, 1.f, "%.2f");
                        }
                        ImGui::EndDisabled();
                        

                    }
                    ImGui::CustomEndChild();
                }
                ImGui::EndGroup();

                ImGui::SameLine();

                ImGui::BeginGroup();
                {
                    // CHAT - Chat spammer and kill say
                    ImGui::CustomBeginChild("CHAT", ImVec2(group_width, chat_panel_h));
                    {
                        ApraGui::SectionHeader("Chat Spammer");
                        ImGui::Spacing();
                        ApraGui::CheckBox("Enable Spammer", &Config::b(g_Variables.m_Misc.m_bChatSpammer));
                        ImGui::BeginDisabled(!Config::b(g_Variables.m_Misc.m_bChatSpammer));
                        {
                            ApraGui::CheckBox("Team Chat Only", &Config::b(g_Variables.m_Misc.m_bChatSpamTeam));
                            ImGui::SliderInt("Delay (ms)", &Config::i(g_Variables.m_Misc.m_iChatSpamDelay), 500, 10000);
                            ImGui::InputTextEx("##chatmsg", "Spam Message", g_Variables.m_Misc.m_szChatSpamMessage, sizeof(g_Variables.m_Misc.m_szChatSpamMessage), ImVec2(-1, 28), NULL);
                        }
                        ImGui::EndDisabled();

                        ImGui::Spacing();
                        ApraGui::SectionHeader("Kill Say");
                        ImGui::Spacing();
                        ApraGui::CheckBox("Enable Kill Say", &Config::b(g_Variables.m_Misc.m_bKillSay));
                        ImGui::BeginDisabled(!Config::b(g_Variables.m_Misc.m_bKillSay));
                        {
                            ApraGui::CheckBox("Team Chat", &Config::b(g_Variables.m_Misc.m_bKillSayTeam));
                            ImGui::InputTextEx("##killmsg", "Kill Message", g_Variables.m_Misc.m_szKillSayMessage, sizeof(g_Variables.m_Misc.m_szKillSayMessage), ImVec2(-1, 28), NULL);
                        }
                        ImGui::EndDisabled();
                    }
                    ImGui::CustomEndChild();

                    // EXTRAS - Additional misc features (non-duplicated)
                    ImGui::CustomBeginChild("EXTRAS", ImVec2(group_width, extras_panel_h));
                    {
                        ApraGui::SectionHeader("Quality of Life");
                        ImGui::Spacing();
                        ApraGui::CheckBox("Auto Accept", &Config::b(g_Variables.m_Misc.m_bAutoAccept));
                        ApraGui::CheckBox("Unlock Inventory", &Config::b(g_Variables.m_Misc.m_bUnlockInventory));
                        ApraGui::CheckBox("Performance Mode", &Config::b(g_Variables.m_Misc.m_bPerformanceMode));
                        ImGui::Spacing();
                        ImGui::Separator();
                        ImGui::Spacing();
                        ApraGui::SectionHeader("Hit Marker");
                        ImGui::Spacing();
                        ApraGui::CheckBox("Enable Hit Marker", &Config::b(g_Variables.m_Misc.m_bHitMarker));
                        ImGui::BeginDisabled(!Config::b(g_Variables.m_Misc.m_bHitMarker));
                        {
                            float colHit[4] = { Config::c(g_Variables.m_Misc.m_colHitMarker).rBase(), Config::c(g_Variables.m_Misc.m_colHitMarker).gBase(), Config::c(g_Variables.m_Misc.m_colHitMarker).bBase(), Config::c(g_Variables.m_Misc.m_colHitMarker).aBase() };
                            if (ImGui::ColorEdit4("Color##HitMarker", colHit, ImGuiColorEditFlags_NoInputs | ImGuiColorEditFlags_AlphaBar))
                                Config::c(g_Variables.m_Misc.m_colHitMarker) = Color(colHit[0], colHit[1], colHit[2], colHit[3]);
                        }
                        ImGui::EndDisabled();
                        
#ifdef _DEBUG
                        ImGui::Spacing();
                        ImGui::Separator();
                        ImGui::Spacing();
                        if (ImGui::Button(X("Debug Commands"), ImVec2(-1, 32))) {
                            Interfaces::m_pEngine->ExecuteClientCmdUnrestricted(std::string(
                                std::string("sv_cheats 1; ") +
                                std::string("bot_kick; ") +
                                std::string("bot_stop 1; ") +
                                std::string("bot_add; ") +
                                std::string("sv_quantize_movement_input 0; ") +
                                std::string("sv_airaccelerate inf; ") +
                                std::string("sv_steamauth_enforce 0; ") +
                                std::string("mp_respawn_immunitytime -1; ")
                            ).c_str());
                        }
#endif
                    }
                    ImGui::CustomEndChild();
                }
                ImGui::EndGroup();
            }
        }
        // === SETTINGS TAB CONTENT ===
        else if (tabs == 4) {
            // CONFIGS SUBTAB
            if (settings_subtabs == 0) {
                static int selected_cfg = -1;

                ImGui::BeginGroup();
                {
                    ImGui::CustomBeginChild("CONFIGS LIST", ImVec2(group_width, group_height_full));
                    {
                        if (ImGui::BeginListBox("##configs_list", ImVec2(-1, -ImGui::GetFrameHeightWithSpacing() - 10)))
                        {
                            for (int i = 0; i < Config::vecFileNames.size(); ++i) {
                                bool is_selected = (selected_cfg == i);
                                if (ImGui::Selectable(Config::vecFileNames[i].c_str(), is_selected)) {
                                    selected_cfg = i;
                                }
                                if (is_selected)
                                    ImGui::SetItemDefaultFocus();
                            }
                            ImGui::EndListBox();
                        }

                        {
                            float btnWidth = ApraGui::CalcButtonWidth("Refresh", 120.0f);
                            float btnHeight = 40.0f;
                            if (ImGui::GetContentRegionAvail().x < btnWidth)
                                btnWidth = -1.0f;
                            else
                                ApraGui::RightAlign(btnWidth);

                            if (ImGui::Button("Refresh", ImVec2(btnWidth, btnHeight))) {
                            Config::Refresh();
                            selected_cfg = -1;
                            }
                        }
                    }
                    ImGui::CustomEndChild();
                }
                ImGui::EndGroup();

                ImGui::SameLine();

                ImGui::BeginGroup();
                {
                    ImGui::CustomBeginChild("CONFIG ACTIONS", ImVec2(group_width, group_height_full));
                    {
                        static bool showDeleteConfirm = false;
                        static std::string configToDelete = "";
                        ApraGui::SectionHeader("Create New Config");
                        ImGui::Spacing();
                        static char new_cfg_name[64] = "";

                        if (ImGui::InputTextEx("##newcfg", "Enter config name", new_cfg_name, sizeof(new_cfg_name), ImVec2(ImGui::GetContentRegionMax().x - style->WindowPadding.x, 40), NULL));

                        {
                            float btnWidth = ApraGui::CalcButtonWidth("Create", 120.0f);
                            float btnHeight = 35.0f;
                            if (ImGui::GetContentRegionAvail().x < btnWidth)
                                btnWidth = -1.0f;
                            else
                                ApraGui::RightAlign(btnWidth);

                            if (ImGui::Button("Create", ImVec2(btnWidth, btnHeight))) {
                            if (strlen(new_cfg_name) > 0) {
                                Config::Save(new_cfg_name, false);
                                Config::Refresh();
                                new_cfg_name[0] = '\0';
                            }
                            }
                        }

                        ImGui::Spacing();
                        ApraGui::SectionHeader("Selected Config Actions");
                        ImGui::Spacing();
                        if (selected_cfg != -1) {
                            {
                                float btnWidth = ApraGui::CalcButtonWidth("Load", 120.0f);
                                float btnHeight = 35.0f;
                                if (ImGui::GetContentRegionAvail().x < btnWidth)
                                    btnWidth = -1.0f;
                                else
                                    ApraGui::RightAlign(btnWidth);

                                if (ImGui::Button("Load", ImVec2(btnWidth, btnHeight))) {
                                Config::Load(Config::vecFileNames[selected_cfg], true);
                                InventoryChanger::m_bWantsHUDUpdate = true;
                                InventoryChanger::OnConfigLoad(Config::vecFileNames.at(selected_cfg));
                                }
                            }

                            {
                                float btnWidth = ApraGui::CalcButtonWidth("Save", 120.0f);
                                float btnHeight = 35.0f;
                                if (ImGui::GetContentRegionAvail().x < btnWidth)
                                    btnWidth = -1.0f;
                                else
                                    ApraGui::RightAlign(btnWidth);

                                if (ImGui::Button("Save", ImVec2(btnWidth, btnHeight))) {
                                Config::Save(Config::vecFileNames[selected_cfg], true);
                                InventoryChanger::m_bWantsHUDUpdate = true;
                                InventoryChanger::OnConfigSave(Config::vecFileNames.at(selected_cfg));
                                }
                            }

                            // Delete with confirmation
                            if (!showDeleteConfirm) {
                                float btnWidth = ApraGui::CalcButtonWidth("Delete", 120.0f);
                                float btnHeight = 35.0f;
                                if (ImGui::GetContentRegionAvail().x < btnWidth)
                                    btnWidth = -1.0f;
                                else
                                    ApraGui::RightAlign(btnWidth);

                                if (ImGui::Button("Delete", ImVec2(btnWidth, btnHeight))) {
                                    showDeleteConfirm = true;
                                    configToDelete = Config::vecFileNames[selected_cfg];
                                }
                            } else {
                                ImGui::TextColored(ImVec4(1.0f, 0.4f, 0.4f, 1.0f), "Delete '%s'?", configToDelete.c_str());
                                const float btnHeight = 30.0f;
                                const float minBtnWidth = 90.0f;
                                const float noWidth = ApraGui::CalcButtonWidth("No", minBtnWidth);
                                const float yesWidth = ApraGui::CalcButtonWidth("Yes", minBtnWidth);
                                const float spacingX = ImGui::GetStyle().ItemSpacing.x;
                                const float totalWidth = noWidth + spacingX + yesWidth;

                                if (ImGui::GetContentRegionAvail().x < totalWidth) {
                                    ApraGui::RightAlign(noWidth);
                                    if (ImGui::Button("No", ImVec2(noWidth, btnHeight))) {
                                        showDeleteConfirm = false;
                                        configToDelete = "";
                                    }

                                    ApraGui::RightAlign(yesWidth);
                                    if (ImGui::Button("Yes", ImVec2(yesWidth, btnHeight))) {
                                    Config::Remove(configToDelete);
                                    Config::Refresh();
                                    selected_cfg = -1;
                                    showDeleteConfirm = false;
                                    configToDelete = "";
                                }
                                } else {
                                    ApraGui::RightAlign(totalWidth);
                                    if (ImGui::Button("No", ImVec2(noWidth, btnHeight))) {
                                        showDeleteConfirm = false;
                                        configToDelete = "";
                                    }
                                    ImGui::SameLine();
                                    if (ImGui::Button("Yes", ImVec2(yesWidth, btnHeight))) {
                                        Config::Remove(configToDelete);
                                        Config::Refresh();
                                        selected_cfg = -1;
                                        showDeleteConfirm = false;
                                        configToDelete = "";
                                    }
                                }
                            }
                            

                        }
                        else {
                            ImGui::PushStyleVar(ImGuiStyleVar_Alpha, 0.5f);
                            {
                                float btnWidth = ApraGui::CalcButtonWidth("Load", 120.0f);
                                float btnHeight = 35.0f;
                                if (ImGui::GetContentRegionAvail().x < btnWidth)
                                    btnWidth = -1.0f;
                                else
                                    ApraGui::RightAlign(btnWidth);
                                ImGui::Button("Load", ImVec2(btnWidth, btnHeight));
                            }
                            {
                                float btnWidth = ApraGui::CalcButtonWidth("Save", 120.0f);
                                float btnHeight = 35.0f;
                                if (ImGui::GetContentRegionAvail().x < btnWidth)
                                    btnWidth = -1.0f;
                                else
                                    ApraGui::RightAlign(btnWidth);
                                ImGui::Button("Save", ImVec2(btnWidth, btnHeight));
                            }
                            {
                                float btnWidth = ApraGui::CalcButtonWidth("Delete", 120.0f);
                                float btnHeight = 35.0f;
                                if (ImGui::GetContentRegionAvail().x < btnWidth)
                                    btnWidth = -1.0f;
                                else
                                    ApraGui::RightAlign(btnWidth);
                                ImGui::Button("Delete", ImVec2(btnWidth, btnHeight));
                            }
                            ImGui::PopStyleVar();
                            ImGui::Spacing();
                            ImGui::TextColored(ImVec4(0.5f, 0.5f, 0.5f, 1.0f), "Select a config first");
                        }
                    }
                    ImGui::CustomEndChild();
                }
                ImGui::EndGroup();
            }
            // MENU SUBTAB
            else if (settings_subtabs == 1) {
                ImGui::BeginGroup();
                {
                    ImGui::CustomBeginChild("THEME", ImVec2(group_width, 200.f));
                    {
                        ApraGui::SectionHeader("Accent");
                        ImGui::Spacing();
                        
                        float accentCol[4] = { c::accent.x, c::accent.y, c::accent.z, c::accent.w };
                        if (ImGui::ColorEdit4("##Accent", accentCol, ImGuiColorEditFlags_NoInputs | ImGuiColorEditFlags_AlphaBar)) {
                            c::accent = ImVec4(accentCol[0], accentCol[1], accentCol[2], accentCol[3]);
                        }
                        ImGui::SameLine(); ImGui::Text("Accent Color");
                        
                        ImGui::Spacing();
                        ApraGui::SectionHeader("Presets");
                        
                        const float swatchW = 55.0f;
                        const float swatchH = 22.0f;
                        
                        if (ImGui::Button("Purple", ImVec2(swatchW, swatchH))) c::accent = ImVec4(0.6f, 0.2f, 0.8f, 1.0f);
                        ImGui::SameLine();
                        if (ImGui::Button("Blue", ImVec2(swatchW, swatchH))) c::accent = ImVec4(0.2f, 0.4f, 1.0f, 1.0f);
                        ImGui::SameLine();
                        if (ImGui::Button("Red", ImVec2(swatchW, swatchH))) c::accent = ImVec4(1.0f, 0.2f, 0.2f, 1.0f);
                        
                        if (ImGui::Button("Green", ImVec2(swatchW, swatchH))) c::accent = ImVec4(0.2f, 0.8f, 0.2f, 1.0f);
                        ImGui::SameLine();
                        if (ImGui::Button("Orange", ImVec2(swatchW, swatchH))) c::accent = ImVec4(1.0f, 0.5f, 0.0f, 1.0f);
                        ImGui::SameLine();
                        if (ImGui::Button("Pink", ImVec2(swatchW, swatchH))) c::accent = ImVec4(1.0f, 0.4f, 0.7f, 1.0f);
                        
                        if (ImGui::Button("Cyan", ImVec2(swatchW, swatchH))) c::accent = ImVec4(0.0f, 0.9f, 0.9f, 1.0f);
                        ImGui::SameLine();
                        if (ImGui::Button("Yellow", ImVec2(swatchW, swatchH))) c::accent = ImVec4(0.87f, 1.0f, 0.04f, 1.0f);
                        ImGui::SameLine();
                        if (ImGui::Button("White", ImVec2(swatchW, swatchH))) c::accent = ImVec4(1.0f, 1.0f, 1.0f, 1.0f);
                    }
                    ImGui::CustomEndChild();

                    ImGui::CustomBeginChild("TEXT", ImVec2(group_width, group_height_full - 212.f));
                    {
                        ApraGui::SectionHeader("Text Colors");
                        ImGui::Spacing();
                        
                        float textActiveCol[4] = { c::text::text_active.x, c::text::text_active.y, c::text::text_active.z, c::text::text_active.w };
                        if (ImGui::ColorEdit4("##TextActive", textActiveCol, ImGuiColorEditFlags_NoInputs | ImGuiColorEditFlags_AlphaBar)) {
                            c::text::text_active = ImVec4(textActiveCol[0], textActiveCol[1], textActiveCol[2], textActiveCol[3]);
                        }
                        ImGui::SameLine(); ImGui::Text("Active Text");
                        
                        float textHovCol[4] = { c::text::text_hov.x, c::text::text_hov.y, c::text::text_hov.z, c::text::text_hov.w };
                        if (ImGui::ColorEdit4("##TextHover", textHovCol, ImGuiColorEditFlags_NoInputs | ImGuiColorEditFlags_AlphaBar)) {
                            c::text::text_hov = ImVec4(textHovCol[0], textHovCol[1], textHovCol[2], textHovCol[3]);
                        }
                        ImGui::SameLine(); ImGui::Text("Hover Text");
                        
                        float textCol[4] = { c::text::text.x, c::text::text.y, c::text::text.z, c::text::text.w };
                        if (ImGui::ColorEdit4("##TextInactive", textCol, ImGuiColorEditFlags_NoInputs | ImGuiColorEditFlags_AlphaBar)) {
                            c::text::text = ImVec4(textCol[0], textCol[1], textCol[2], textCol[3]);
                        }
                        ImGui::SameLine(); ImGui::Text("Inactive Text");
                    }
                    ImGui::CustomEndChild();
                }
                ImGui::EndGroup();

                ImGui::SameLine();

                ImGui::BeginGroup();
                {
                    ImGui::CustomBeginChild("BACKGROUNDS", ImVec2(group_width, 180.f));
                    {
                        ApraGui::SectionHeader("Background Colors");
                        ImGui::Spacing();
                        
                        float bgCol[4] = { c::bg::background.x, c::bg::background.y, c::bg::background.z, c::bg::background.w };
                        if (ImGui::ColorEdit4("##WindowBg", bgCol, ImGuiColorEditFlags_NoInputs | ImGuiColorEditFlags_AlphaBar)) {
                            c::bg::background = ImVec4(bgCol[0], bgCol[1], bgCol[2], bgCol[3]);
                        }
                        ImGui::SameLine(); ImGui::Text("Window");
                        
                        float childCol[4] = { c::child::background.x, c::child::background.y, c::child::background.z, c::child::background.w };
                        if (ImGui::ColorEdit4("##PanelBg", childCol, ImGuiColorEditFlags_NoInputs | ImGuiColorEditFlags_AlphaBar)) {
                            c::child::background = ImVec4(childCol[0], childCol[1], childCol[2], childCol[3]);
                        }
                        ImGui::SameLine(); ImGui::Text("Panels");
                        
                        float outlineCol[4] = { c::child::outline.x, c::child::outline.y, c::child::outline.z, c::child::outline.w };
                        if (ImGui::ColorEdit4("##Outline", outlineCol, ImGuiColorEditFlags_NoInputs | ImGuiColorEditFlags_AlphaBar)) {
                            c::child::outline = ImVec4(outlineCol[0], outlineCol[1], outlineCol[2], outlineCol[3]);
                        }
                        ImGui::SameLine(); ImGui::Text("Outline");
                    }
                    ImGui::CustomEndChild();

                    ImGui::CustomBeginChild("CONTROLS", ImVec2(group_width, 180.f));
                    {
                        ApraGui::SectionHeader("Control Colors");
                        ImGui::Spacing();
                        
                        float btnCol[4] = { c::button::background_inactive.x, c::button::background_inactive.y, c::button::background_inactive.z, c::button::background_inactive.w };
                        if (ImGui::ColorEdit4("##BtnBg", btnCol, ImGuiColorEditFlags_NoInputs | ImGuiColorEditFlags_AlphaBar)) {
                            c::button::background_inactive = ImVec4(btnCol[0], btnCol[1], btnCol[2], btnCol[3]);
                        }
                        ImGui::SameLine(); ImGui::Text("Buttons");
                        
                        float sliderCol[4] = { c::slider::background.x, c::slider::background.y, c::slider::background.z, c::slider::background.w };
                        if (ImGui::ColorEdit4("##SliderBg", sliderCol, ImGuiColorEditFlags_NoInputs | ImGuiColorEditFlags_AlphaBar)) {
                            c::slider::background = ImVec4(sliderCol[0], sliderCol[1], sliderCol[2], sliderCol[3]);
                        }
                        ImGui::SameLine(); ImGui::Text("Sliders");
                        
                        float inputCol[4] = { c::input::background_inactive.x, c::input::background_inactive.y, c::input::background_inactive.z, c::input::background_inactive.w };
                        if (ImGui::ColorEdit4("##InputBg", inputCol, ImGuiColorEditFlags_NoInputs | ImGuiColorEditFlags_AlphaBar)) {
                            c::input::background_inactive = ImVec4(inputCol[0], inputCol[1], inputCol[2], inputCol[3]);
                        }
                        ImGui::SameLine(); ImGui::Text("Inputs");
                    }
                    ImGui::CustomEndChild();

                    ImGui::CustomBeginChild("RESET", ImVec2(group_width, group_height_full - 372.f));
                    {
                        ApraGui::SectionHeader("Reset");
                        ImGui::Spacing();
                        
                        if (ImGui::Button("Reset All Colors", ImVec2(-1, 30))) {
                            c::accent = ImVec4(0.0f, 0.9f, 0.9f, 1.0f);
                            c::bg::background = ImColor(14, 14, 16, 255);
                            c::child::background = ImColor(18, 18, 22, 255);
                            c::child::outline = ImColor(255, 255, 255, 8);
                            c::text::text_active = ImColor(255, 255, 255, 255);
                            c::text::text_hov = ImColor(180, 180, 180, 255);
                            c::text::text = ImColor(120, 120, 130, 255);
                            c::button::background_inactive = ImColor(32, 32, 38, 255);
                            c::slider::background = ImColor(28, 28, 32, 255);
                            c::input::background_inactive = ImColor(28, 28, 32, 255);
                        }
                    }
                    ImGui::CustomEndChild();
                }
                ImGui::EndGroup();
            }
        }
    }
    ImGui::End();
    ImGui::PopFont();
    ImGui::PopStyleVar(4);
}

void Gui::Render()
{
    ImGuiIO& io = ImGui::GetIO();
    Gui::Update(io);

    if (!m_bInitialized)
        return;

    // blurEffect.BeginBlur();

    DrawHitLogs();
    RenderMenu();

    // blurEffect.EndBlur();

    ApraGui::RenderNotifications();
}
