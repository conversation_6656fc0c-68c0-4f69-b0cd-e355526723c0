{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#proc-macro2@1.0.103","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\proc-macro2-1.0.103\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\proc-macro2-1.0.103\\build.rs","edition":"2021","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","proc-macro"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\proc-macro2-662130a2653d2893\\build-script-build.exe","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\proc-macro2-662130a2653d2893\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#proc-macro2@1.0.103","linked_libs":[],"linked_paths":[],"cfgs":["wrap_proc_macro","proc_macro_span_location","proc_macro_span_file"],"env":[],"out_dir":"C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\proc-macro2-9d70e0f362d1d34a\\out"}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#unicode-ident@1.0.22","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\unicode-ident-1.0.22\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"unicode_ident","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\unicode-ident-1.0.22\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libunicode_ident-fca7e2cb9213e814.rlib","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libunicode_ident-fca7e2cb9213e814.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#quote@1.0.42","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\quote-1.0.42\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\quote-1.0.42\\build.rs","edition":"2018","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","proc-macro"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\quote-d5f70ebd4af01072\\build-script-build.exe","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\quote-d5f70ebd4af01072\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#cfg-if@1.0.4","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\cfg-if-1.0.4\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"cfg_if","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\cfg-if-1.0.4\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libcfg_if-5ecc31d12182ec9f.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#windows-link@0.2.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows-link-0.2.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"windows_link","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows-link-0.2.1\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libwindows_link-9ed54d9b9d411584.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#serde_core@1.0.228","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\serde_core-1.0.228\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\serde_core-1.0.228\\build.rs","edition":"2021","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["result","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\serde_core-e74f098d53a03cb2\\build-script-build.exe","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\serde_core-e74f098d53a03cb2\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#once_cell@1.21.3","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\once_cell-1.21.3\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"once_cell","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\once_cell-1.21.3\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","default","race","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libonce_cell-f7f483389bf83082.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#stable_deref_trait@1.2.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\stable_deref_trait-1.2.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"stable_deref_trait","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\stable_deref_trait-1.2.1\\src\\lib.rs","edition":"2015","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libstable_deref_trait-0f50cabd25ee1546.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#smallvec@1.15.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\smallvec-1.15.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"smallvec","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\smallvec-1.15.1\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["const_generics","const_new"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libsmallvec-d0dcb374a096c8dc.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#windows_x86_64_msvc@0.53.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows_x86_64_msvc-0.53.1\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows_x86_64_msvc-0.53.1\\build.rs","edition":"2021","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\windows_x86_64_msvc-b23398d0344d1780\\build-script-build.exe","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\windows_x86_64_msvc-b23398d0344d1780\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#pin-project-lite@0.2.16","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pin-project-lite-0.2.16\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"pin_project_lite","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pin-project-lite-0.2.16\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libpin_project_lite-5648343fb15d0bf4.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#windows_x86_64_msvc@0.52.6","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows_x86_64_msvc-0.52.6\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows_x86_64_msvc-0.52.6\\build.rs","edition":"2021","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\windows_x86_64_msvc-9dfb474e520ab7bc\\build-script-build.exe","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\windows_x86_64_msvc-9dfb474e520ab7bc\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#version_check@0.9.5","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\version_check-0.9.5\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"version_check","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\version_check-0.9.5\\src\\lib.rs","edition":"2015","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libversion_check-2f1fee6dbc612d2b.rlib","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libversion_check-2f1fee6dbc612d2b.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#autocfg@1.5.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\autocfg-1.5.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"autocfg","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\autocfg-1.5.0\\src\\lib.rs","edition":"2015","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libautocfg-830af75a3e50bff4.rlib","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libautocfg-830af75a3e50bff4.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#cfg_aliases@0.2.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\cfg_aliases-0.2.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"cfg_aliases","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\cfg_aliases-0.2.1\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libcfg_aliases-cc673323a395fe0c.rlib","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libcfg_aliases-cc673323a395fe0c.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#bitflags@2.10.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\bitflags-2.10.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"bitflags","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\bitflags-2.10.0\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libbitflags-84421c89782dbcfc.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#proc-macro2@1.0.103","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\proc-macro2-1.0.103\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"proc_macro2","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\proc-macro2-1.0.103\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","proc-macro"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libproc_macro2-3bbf675566cacad0.rlib","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libproc_macro2-3bbf675566cacad0.rmeta"],"executable":null,"fresh":true}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#quote@1.0.42","linked_libs":[],"linked_paths":[],"cfgs":[],"env":[],"out_dir":"C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\quote-d5f3d4bc2c46ccbb\\out"}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#serde_core@1.0.228","linked_libs":[],"linked_paths":[],"cfgs":[],"env":[],"out_dir":"C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\serde_core-795ad042a468be4f\\out"}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#windows_x86_64_msvc@0.53.1","linked_libs":[],"linked_paths":["native=C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows_x86_64_msvc-0.53.1\\lib"],"cfgs":[],"env":[],"out_dir":"C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\windows_x86_64_msvc-c3ba7d662151df3a\\out"}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#windows_x86_64_msvc@0.52.6","linked_libs":[],"linked_paths":["native=C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows_x86_64_msvc-0.52.6\\lib"],"cfgs":[],"env":[],"out_dir":"C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\windows_x86_64_msvc-fe67252328297343\\out"}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#khronos_api@3.1.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\khronos_api-3.1.0\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\khronos_api-3.1.0\\build.rs","edition":"2015","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\khronos_api-4265fe95850fbf18\\build-script-build.exe","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\khronos_api-4265fe95850fbf18\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#litemap@0.8.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\litemap-0.8.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"litemap","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\litemap-0.8.1\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\liblitemap-8e7c535159872f42.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#bytes@1.11.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\bytes-1.11.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"bytes","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\bytes-1.11.0\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libbytes-0586e3385bd28bbc.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#writeable@0.6.2","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\writeable-0.6.2\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"writeable","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\writeable-0.6.2\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libwriteable-26297ba5e9ecffb0.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#crc32fast@1.5.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\crc32fast-1.5.0\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\crc32fast-1.5.0\\build.rs","edition":"2021","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\crc32fast-2765995a9854f72d\\build-script-build.exe","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\crc32fast-2765995a9854f72d\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#icu_normalizer_data@2.1.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\icu_normalizer_data-2.1.1\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\icu_normalizer_data-2.1.1\\build.rs","edition":"2021","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\icu_normalizer_data-742037d3d86e3a2a\\build-script-build.exe","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\icu_normalizer_data-742037d3d86e3a2a\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#icu_properties_data@2.1.2","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\icu_properties_data-2.1.2\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\icu_properties_data-2.1.2\\build.rs","edition":"2021","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\icu_properties_data-4f6e2491d1cd574e\\build-script-build.exe","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\icu_properties_data-4f6e2491d1cd574e\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#find-msvc-tools@0.1.5","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\find-msvc-tools-0.1.5\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"find_msvc_tools","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\find-msvc-tools-0.1.5\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libfind_msvc_tools-b83ae4636bb1705c.rlib","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libfind_msvc_tools-b83ae4636bb1705c.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#serde@1.0.228","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\serde-1.0.228\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\serde-1.0.228\\build.rs","edition":"2021","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","derive","serde_derive","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\serde-5722ef58355d4eab\\build-script-build.exe","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\serde-5722ef58355d4eab\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#xml-rs@0.8.28","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\xml-rs-0.8.28\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"xml","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\xml-rs-0.8.28\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libxml-751a03d30c37f38b.rlib","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libxml-751a03d30c37f38b.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#futures-core@0.3.31","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\futures-core-0.3.31\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"futures_core","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\futures-core-0.3.31\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","default","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libfutures_core-8090a8620636936a.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#quote@1.0.42","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\quote-1.0.42\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"quote","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\quote-1.0.42\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","proc-macro"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libquote-0859780ba1545100.rlib","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libquote-0859780ba1545100.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#windows_x86_64_msvc@0.53.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows_x86_64_msvc-0.53.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"windows_x86_64_msvc","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows_x86_64_msvc-0.53.1\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libwindows_x86_64_msvc-a76938dc0a8fbc29.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#windows_x86_64_msvc@0.52.6","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows_x86_64_msvc-0.52.6\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"windows_x86_64_msvc","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows_x86_64_msvc-0.52.6\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libwindows_x86_64_msvc-77fa738284360423.rmeta"],"executable":null,"fresh":true}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#khronos_api@3.1.0","linked_libs":[],"linked_paths":[],"cfgs":[],"env":[],"out_dir":"C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\khronos_api-ccffc70c0040d941\\out"}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#crc32fast@1.5.0","linked_libs":[],"linked_paths":[],"cfgs":["stable_arm_crc32_intrinsics"],"env":[],"out_dir":"C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\crc32fast-339df78aacbb103b\\out"}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#serde_core@1.0.228","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\serde_core-1.0.228\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"serde_core","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\serde_core-1.0.228\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["result","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libserde_core-4ae820a565559ec4.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#log@0.4.29","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\log-0.4.29\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"log","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\log-0.4.29\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\liblog-7cd82ba777412f24.rlib","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\liblog-7cd82ba777412f24.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#log@0.4.29","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\log-0.4.29\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"log","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\log-0.4.29\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\liblog-8992c81f97a8b019.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#itoa@1.0.16","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\itoa-1.0.16\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"itoa","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\itoa-1.0.16\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libitoa-dcdbd4ea282419d9.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#shlex@1.3.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\shlex-1.3.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"shlex","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\shlex-1.3.0\\src\\lib.rs","edition":"2015","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libshlex-c379f0df54da8dc3.rlib","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libshlex-c379f0df54da8dc3.rmeta"],"executable":null,"fresh":true}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#serde@1.0.228","linked_libs":[],"linked_paths":[],"cfgs":["if_docsrs_then_no_serde_core"],"env":[],"out_dir":"C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\serde-eb917dd564a7a0e2\\out"}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#icu_properties_data@2.1.2","linked_libs":[],"linked_paths":[],"cfgs":[],"env":[],"out_dir":"C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\icu_properties_data-53509ccbb1356bb8\\out"}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#icu_normalizer_data@2.1.1","linked_libs":[],"linked_paths":[],"cfgs":[],"env":[],"out_dir":"C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\icu_normalizer_data-ea3df7f8d0e71208\\out"}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#tracing-core@0.1.36","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tracing-core-0.1.36\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"tracing_core","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tracing-core-0.1.36\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["once_cell","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libtracing_core-ce60ad6d1b7ffd01.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#windows-sys@0.61.2","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows-sys-0.61.2\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"windows_sys","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows-sys-0.61.2\\src\\lib.rs","edition":"2021","doc":true,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["Wdk","Wdk_Foundation","Wdk_Storage","Wdk_Storage_FileSystem","Wdk_System","Wdk_System_IO","Win32","Win32_Foundation","Win32_Networking","Win32_Networking_WinSock","Win32_Security","Win32_Storage","Win32_Storage_FileSystem","Win32_System","Win32_System_IO","Win32_System_Pipes","Win32_System_SystemServices","Win32_System_WindowsProgramming","default"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libwindows_sys-98a95c1bbf27b14c.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#percent-encoding@2.3.2","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\percent-encoding-2.3.2\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"percent_encoding","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\percent-encoding-2.3.2\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","default","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libpercent_encoding-8812c46d869532e9.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#syn@2.0.111","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\syn-2.0.111\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"syn","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\syn-2.0.111\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["clone-impls","default","derive","extra-traits","fold","full","parsing","printing","proc-macro","visit"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libsyn-5f636584df8545cc.rlib","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libsyn-5f636584df8545cc.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#windows-targets@0.53.5","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows-targets-0.53.5\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"windows_targets","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows-targets-0.53.5\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libwindows_targets-19f1dcbe96269337.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#windows-targets@0.52.6","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows-targets-0.52.6\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"windows_targets","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows-targets-0.52.6\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libwindows_targets-66ad930aaba7462d.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#khronos_api@3.1.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\khronos_api-3.1.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"khronos_api","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\khronos_api-3.1.0\\src\\lib.rs","edition":"2015","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libkhronos_api-d42e7606fc88840d.rlib","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libkhronos_api-d42e7606fc88840d.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#cc@1.2.50","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\cc-1.2.50\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"cc","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\cc-1.2.50\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libcc-5b95116bcb48bd87.rlib","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libcc-5b95116bcb48bd87.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#zerocopy@0.8.31","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\zerocopy-0.8.31\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\zerocopy-0.8.31\\build.rs","edition":"2021","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["simd"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\zerocopy-f5b05c8a8406d451\\build-script-build.exe","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\zerocopy-f5b05c8a8406d451\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#parking_lot_core@0.9.12","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\parking_lot_core-0.9.12\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\parking_lot_core-0.9.12\\build.rs","edition":"2021","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\parking_lot_core-35f23ffc2aaea581\\build-script-build.exe","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\parking_lot_core-35f23ffc2aaea581\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#mio@1.1.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\mio-1.1.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"mio","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\mio-1.1.1\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["net","os-ext","os-poll"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libmio-05bbe05ba5065f11.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#icu_normalizer_data@2.1.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\icu_normalizer_data-2.1.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"icu_normalizer_data","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\icu_normalizer_data-2.1.1\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libicu_normalizer_data-9fe99eea73837acf.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#icu_properties_data@2.1.2","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\icu_properties_data-2.1.2\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"icu_properties_data","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\icu_properties_data-2.1.2\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libicu_properties_data-de82647aabeec37b.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#tracing@0.1.44","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tracing-0.1.44\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"tracing","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tracing-0.1.44\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libtracing-74da619cbfaa88cf.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#ahash@0.8.12","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\ahash-0.8.12\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\ahash-0.8.12\\build.rs","edition":"2018","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["no-rng","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\ahash-3ee753fe03202ddd\\build-script-build.exe","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\ahash-3ee753fe03202ddd\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#scopeguard@1.2.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\scopeguard-1.2.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"scopeguard","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\scopeguard-1.2.0\\src\\lib.rs","edition":"2015","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libscopeguard-fa7b197cf7b55f28.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#accesskit@0.16.3","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\accesskit-0.16.3\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"accesskit","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\accesskit-0.16.3\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libaccesskit-afc9bc678d0198fa.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#futures-sink@0.3.31","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\futures-sink-0.3.31\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"futures_sink","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\futures-sink-0.3.31\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libfutures_sink-421b016d3f7f2e4a.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#ttf-parser@0.25.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\ttf-parser-0.25.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"ttf_parser","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\ttf-parser-0.25.1\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["apple-layout","glyph-names","gvar-alloc","opentype-layout","std","variable-fonts"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libttf_parser-e5ff632b91982256.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#synstructure@0.13.2","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\synstructure-0.13.2\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"synstructure","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\synstructure-0.13.2\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","proc-macro"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libsynstructure-c1cf0e812e278ae6.rlib","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libsynstructure-c1cf0e812e278ae6.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#zerovec-derive@0.11.2","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\zerovec-derive-0.11.2\\Cargo.toml","target":{"kind":["proc-macro"],"crate_types":["proc-macro"],"name":"zerovec_derive","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\zerovec-derive-0.11.2\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\zerovec_derive-b85474998d972b56.dll","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\zerovec_derive-b85474998d972b56.dll.lib","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\zerovec_derive-b85474998d972b56.dll.exp","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\zerovec_derive-b85474998d972b56.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#displaydoc@0.2.5","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\displaydoc-0.2.5\\Cargo.toml","target":{"kind":["proc-macro"],"crate_types":["proc-macro"],"name":"displaydoc","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\displaydoc-0.2.5\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\displaydoc-13d9e05504bfe699.dll","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\displaydoc-13d9e05504bfe699.dll.lib","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\displaydoc-13d9e05504bfe699.dll.exp","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\displaydoc-13d9e05504bfe699.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#windows-sys@0.60.2","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows-sys-0.60.2\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"windows_sys","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows-sys-0.60.2\\src\\lib.rs","edition":"2021","doc":true,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["Win32","Win32_Foundation","Win32_Networking","Win32_Networking_WinSock","Win32_Storage","Win32_Storage_FileSystem","Win32_System","Win32_System_DataExchange","Win32_System_IO","Win32_System_Memory","Win32_System_Ole","Win32_System_Threading","Win32_System_WindowsProgramming","Win32_UI","Win32_UI_Shell","default"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libwindows_sys-b7c3ee4087aaf66c.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#bytemuck_derive@1.10.2","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\bytemuck_derive-1.10.2\\Cargo.toml","target":{"kind":["proc-macro"],"crate_types":["proc-macro"],"name":"bytemuck_derive","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\bytemuck_derive-1.10.2\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\bytemuck_derive-a18a9828f3ebc952.dll","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\bytemuck_derive-a18a9828f3ebc952.dll.lib","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\bytemuck_derive-a18a9828f3ebc952.dll.exp","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\bytemuck_derive-a18a9828f3ebc952.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#gl_generator@0.14.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\gl_generator-0.14.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"gl_generator","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\gl_generator-0.14.0\\lib.rs","edition":"2015","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libgl_generator-dbe39a4a8bf73df6.rlib","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libgl_generator-dbe39a4a8bf73df6.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#serde_derive@1.0.228","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\serde_derive-1.0.228\\Cargo.toml","target":{"kind":["proc-macro"],"crate_types":["proc-macro"],"name":"serde_derive","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\serde_derive-1.0.228\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\serde_derive-b2a3ee4b44e8a4f1.dll","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\serde_derive-b2a3ee4b44e8a4f1.dll.lib","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\serde_derive-b2a3ee4b44e8a4f1.dll.exp","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\serde_derive-b2a3ee4b44e8a4f1.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#ring@0.17.14","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\ring-0.17.14\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\ring-0.17.14\\build.rs","edition":"2021","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","default","dev_urandom_fallback"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\ring-e02eaf343a5d98d4\\build-script-build.exe","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\ring-e02eaf343a5d98d4\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#parking_lot_core@0.9.12","linked_libs":[],"linked_paths":[],"cfgs":[],"env":[],"out_dir":"C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\parking_lot_core-95af01e4ceb58349\\out"}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#zerocopy@0.8.31","linked_libs":[],"linked_paths":[],"cfgs":[],"env":[],"out_dir":"C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\zerocopy-0c9cd9ce8eec3516\\out"}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#lock_api@0.4.14","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\lock_api-0.4.14\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"lock_api","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\lock_api-0.4.14\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["atomic_usize","default"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\liblock_api-d1a6d4f00235213b.rmeta"],"executable":null,"fresh":true}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#ahash@0.8.12","linked_libs":[],"linked_paths":[],"cfgs":["folded_multiply"],"env":[],"out_dir":"C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\ahash-6447987493d6addf\\out"}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#owned_ttf_parser@0.25.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\owned_ttf_parser-0.25.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"owned_ttf_parser","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\owned_ttf_parser-0.25.1\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["apple-layout","default","glyph-names","gvar-alloc","opentype-layout","std","variable-fonts"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libowned_ttf_parser-1bfcab8c77214db3.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#windows-result@0.2.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows-result-0.2.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"windows_result","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows-result-0.2.0\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libwindows_result-1b3ecae1999de107.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#windows-sys@0.52.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows-sys-0.52.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"windows_sys","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows-sys-0.52.0\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["Win32","Win32_Devices","Win32_Devices_HumanInterfaceDevice","Win32_Foundation","Win32_Globalization","Win32_Graphics","Win32_Graphics_Dwm","Win32_Graphics_Gdi","Win32_Graphics_OpenGL","Win32_Media","Win32_Security","Win32_System","Win32_System_Com","Win32_System_Com_StructuredStorage","Win32_System_LibraryLoader","Win32_System_Ole","Win32_System_SystemInformation","Win32_System_SystemServices","Win32_System_Threading","Win32_System_WindowsProgramming","Win32_UI","Win32_UI_Accessibility","Win32_UI_Controls","Win32_UI_HiDpi","Win32_UI_Input","Win32_UI_Input_Ime","Win32_UI_Input_KeyboardAndMouse","Win32_UI_Input_Pointer","Win32_UI_Input_Touch","Win32_UI_Shell","Win32_UI_TextServices","Win32_UI_WindowsAndMessaging","default"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libwindows_sys-4aa54aac7962665a.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#http@1.4.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\http-1.4.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"http","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\http-1.4.0\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libhttp-dafb453a5821fdec.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#zerofrom-derive@0.1.6","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\zerofrom-derive-0.1.6\\Cargo.toml","target":{"kind":["proc-macro"],"crate_types":["proc-macro"],"name":"zerofrom_derive","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\zerofrom-derive-0.1.6\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\zerofrom_derive-6782ee9071965935.dll","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\zerofrom_derive-6782ee9071965935.dll.lib","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\zerofrom_derive-6782ee9071965935.dll.exp","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\zerofrom_derive-6782ee9071965935.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#yoke-derive@0.8.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\yoke-derive-0.8.1\\Cargo.toml","target":{"kind":["proc-macro"],"crate_types":["proc-macro"],"name":"yoke_derive","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\yoke-derive-0.8.1\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\yoke_derive-bb74465624d69429.dll","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\yoke_derive-bb74465624d69429.dll.lib","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\yoke_derive-bb74465624d69429.dll.exp","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\yoke_derive-bb74465624d69429.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#bytemuck@1.24.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\bytemuck-1.24.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"bytemuck","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\bytemuck-1.24.0\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["bytemuck_derive","derive","extern_crate_alloc"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libbytemuck-9329977af7bb40a6.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#socket2@0.6.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\socket2-0.6.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"socket2","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\socket2-0.6.1\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["all"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libsocket2-b3a4a74e18396808.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#serde@1.0.228","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\serde-1.0.228\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"serde","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\serde-1.0.228\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","derive","serde_derive","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libserde-27b52d1aa40a70d4.rmeta"],"executable":null,"fresh":true}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#ring@0.17.14","linked_libs":["static=ring_core_0_17_14_","static=ring_core_0_17_14__test"],"linked_paths":["native=C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\ring-d08ecc514b5fd7b5\\out"],"cfgs":[],"env":[],"out_dir":"C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\ring-d08ecc514b5fd7b5\\out"}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#zerocopy@0.8.31","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\zerocopy-0.8.31\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"zerocopy","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\zerocopy-0.8.31\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["simd"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libzerocopy-2287c681261f54b7.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#parking_lot_core@0.9.12","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\parking_lot_core-0.9.12\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"parking_lot_core","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\parking_lot_core-0.9.12\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libparking_lot_core-93c9a2ca0b829a20.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#num-traits@0.2.19","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\num-traits-0.2.19\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\num-traits-0.2.19\\build.rs","edition":"2021","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\num-traits-16cf4ed352bbbdb4\\build-script-build.exe","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\num-traits-16cf4ed352bbbdb4\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#pin-utils@0.1.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pin-utils-0.1.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"pin_utils","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pin-utils-0.1.0\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libpin_utils-565db1cb79db5455.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#raw-window-handle@0.6.2","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\raw-window-handle-0.6.2\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"raw_window_handle","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\raw-window-handle-0.6.2\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libraw_window_handle-4fa2228ab4c7273d.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#zeroize@1.8.2","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\zeroize-1.8.2\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"zeroize","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\zeroize-1.8.2\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","default"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libzeroize-59dabedfee9509e3.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#memchr@2.7.6","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\memchr-2.7.6\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"memchr","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\memchr-2.7.6\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","default","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libmemchr-1e6efdcf45b953bd.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#typenum@1.19.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\typenum-1.19.0\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\typenum-1.19.0\\build.rs","edition":"2018","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\typenum-e3c0df5b197a46e0\\build-script-build.exe","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\typenum-e3c0df5b197a46e0\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#ab_glyph_rasterizer@0.1.10","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\ab_glyph_rasterizer-0.1.10\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"ab_glyph_rasterizer","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\ab_glyph_rasterizer-0.1.10\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libab_glyph_rasterizer-e232f06c6fa40122.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#simd-adler32@0.3.8","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\simd-adler32-0.3.8\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"simd_adler32","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\simd-adler32-0.3.8\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["const-generics","default","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libsimd_adler32-79fee578b119fd90.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#zerofrom@0.1.6","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\zerofrom-0.1.6\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"zerofrom","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\zerofrom-0.1.6\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["derive"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libzerofrom-3b92886bd3de99da.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#emath@0.29.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\emath-0.29.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"emath","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\emath-0.29.1\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["bytemuck"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libemath-267149c07cde665a.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#tokio@1.48.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tokio-1.48.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"tokio","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tokio-1.48.0\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["bytes","default","io-util","libc","mio","net","rt","socket2","sync","time","windows-sys"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libtokio-d244365b29eaf073.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#ahash@0.8.12","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\ahash-0.8.12\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"ahash","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\ahash-0.8.12\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["no-rng","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libahash-a85e2044241e7eca.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#rustls-pki-types@1.13.2","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\rustls-pki-types-1.13.2\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"rustls_pki_types","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\rustls-pki-types-1.13.2\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","default","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\librustls_pki_types-e8825a73d531ef8a.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#parking_lot@0.12.5","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\parking_lot-0.12.5\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"parking_lot","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\parking_lot-0.12.5\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libparking_lot-9c294b7f4a757c39.rmeta"],"executable":null,"fresh":true}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#typenum@1.19.0","linked_libs":[],"linked_paths":[],"cfgs":[],"env":[],"out_dir":"C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\typenum-75512d27501f64ec\\out"}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#ab_glyph@0.2.32","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\ab_glyph-0.2.32\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"ab_glyph","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\ab_glyph-0.2.32\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","gvar-alloc","std","variable-fonts"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libab_glyph-515af1b695bf3c0e.rmeta"],"executable":null,"fresh":true}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#num-traits@0.2.19","linked_libs":[],"linked_paths":[],"cfgs":["has_total_cmp"],"env":[],"out_dir":"C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\num-traits-dd98ab60f481715c\\out"}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#http-body@1.0.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\http-body-1.0.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"http_body","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\http-body-1.0.1\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libhttp_body-794e8f8905534d8e.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#windows-strings@0.1.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows-strings-0.1.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"windows_strings","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows-strings-0.1.0\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libwindows_strings-6b191620e79b4ef6.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#windows-implement@0.58.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows-implement-0.58.0\\Cargo.toml","target":{"kind":["proc-macro"],"crate_types":["proc-macro"],"name":"windows_implement","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows-implement-0.58.0\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\windows_implement-8bdfb991b010bd5f.dll","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\windows_implement-8bdfb991b010bd5f.dll.lib","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\windows_implement-8bdfb991b010bd5f.dll.exp","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\windows_implement-8bdfb991b010bd5f.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#windows-interface@0.58.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows-interface-0.58.0\\Cargo.toml","target":{"kind":["proc-macro"],"crate_types":["proc-macro"],"name":"windows_interface","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows-interface-0.58.0\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\windows_interface-0aad4cdce08702f2.dll","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\windows_interface-0aad4cdce08702f2.dll.lib","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\windows_interface-0aad4cdce08702f2.dll.exp","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\windows_interface-0aad4cdce08702f2.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#form_urlencoded@1.2.2","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\form_urlencoded-1.2.2\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"form_urlencoded","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\form_urlencoded-1.2.2\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":false},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","default","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libform_urlencoded-fba38a9a8573079e.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#winit@0.30.12","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\winit-0.30.12\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\winit-0.30.12\\build.rs","edition":"2021","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["ahash","bytemuck","default","memmap2","percent-encoding","rwh_06","sctk","sctk-adwaita","wayland","wayland-backend","wayland-client","wayland-csd-adwaita","wayland-dlopen","wayland-protocols","wayland-protocols-plasma","x11","x11-dl","x11rb"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\winit-d5450de8a71b1162\\build-script-build.exe","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\winit-d5450de8a71b1162\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#getrandom@0.2.16","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\getrandom-0.2.16\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"getrandom","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\getrandom-0.2.16\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libgetrandom-8847b2c88181ceb2.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#yoke@0.8.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\yoke-0.8.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"yoke","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\yoke-0.8.1\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["derive","zerofrom"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libyoke-7d91b28248ce007c.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#ecolor@0.29.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\ecolor-0.29.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"ecolor","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\ecolor-0.29.1\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["bytemuck"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libecolor-8266c7b58227ac59.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#generic-array@0.14.7","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\generic-array-0.14.7\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\generic-array-0.14.7\\build.rs","edition":"2015","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["more_lengths"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\generic-array-2f7aa134ee90b350\\build-script-build.exe","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\generic-array-2f7aa134ee90b350\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#httparse@1.10.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\httparse-1.10.1\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\httparse-1.10.1\\build.rs","edition":"2018","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\httparse-f9e87ac5503171bc\\build-script-build.exe","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\httparse-f9e87ac5503171bc\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#serde@1.0.228","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\serde-1.0.228\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\serde-1.0.228\\build.rs","edition":"2021","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\serde-ddfb1fb97d378ee0\\build-script-build.exe","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\serde-ddfb1fb97d378ee0\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#untrusted@0.9.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\untrusted-0.9.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"untrusted","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\untrusted-0.9.0\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libuntrusted-77edbbdd353c554e.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#utf8_iter@1.0.4","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\utf8_iter-1.0.4\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"utf8_iter","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\utf8_iter-1.0.4\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libutf8_iter-85bb9dd52e26652f.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#nohash-hasher@0.2.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nohash-hasher-0.2.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"nohash_hasher","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nohash-hasher-0.2.0\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libnohash_hasher-1f77f5c1a192ebaa.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#arrayvec@0.7.6","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\arrayvec-0.7.6\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"arrayvec","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\arrayvec-0.7.6\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libarrayvec-74cece0fac30b531.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#futures-task@0.3.31","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\futures-task-0.3.31\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"futures_task","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\futures-task-0.3.31\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libfutures_task-39278d2792bb5793.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#adler2@2.0.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\adler2-2.0.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"adler2","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\adler2-2.0.1\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libadler2-b23bd624afe43c6a.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#futures-io@0.3.31","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\futures-io-0.3.31\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"futures_io","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\futures-io-0.3.31\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libfutures_io-65e65f0f868aa3f4.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#paste@1.0.15","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\paste-1.0.15\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\paste-1.0.15\\build.rs","edition":"2018","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\paste-e859d480aeaf1301\\build-script-build.exe","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\paste-e859d480aeaf1301\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#siphasher@1.0.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\siphasher-1.0.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"siphasher","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\siphasher-1.0.1\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libsiphasher-0bc3cb33e2f3b364.rlib","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libsiphasher-0bc3cb33e2f3b364.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#epaint_default_fonts@0.29.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\epaint_default_fonts-0.29.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"epaint_default_fonts","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\epaint_default_fonts-0.29.1\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libepaint_default_fonts-9b2fe554e6b21913.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#windows_x86_64_msvc@0.48.5","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows_x86_64_msvc-0.48.5\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows_x86_64_msvc-0.48.5\\build.rs","edition":"2018","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\windows_x86_64_msvc-dee80c688397a653\\build-script-build.exe","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\windows_x86_64_msvc-dee80c688397a653\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#zerovec@0.11.5","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\zerovec-0.11.5\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"zerovec","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\zerovec-0.11.5\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["derive","yoke"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libzerovec-03f8439ecbccf0b3.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#zerotrie@0.2.3","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\zerotrie-0.2.3\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"zerotrie","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\zerotrie-0.2.3\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["yoke","zerofrom"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libzerotrie-55b070ebb98af927.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#slab@0.4.11","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\slab-0.4.11\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"slab","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\slab-0.4.11\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libslab-b0f8ceb87b9d2e88.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#unicase@2.8.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\unicase-2.8.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"unicase","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\unicase-2.8.1\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libunicase-ce7cafa201ec64e4.rlib","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libunicase-ce7cafa201ec64e4.rmeta"],"executable":null,"fresh":true}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#paste@1.0.15","linked_libs":[],"linked_paths":[],"cfgs":[],"env":[],"out_dir":"C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\paste-ce799d79fdb52b1a\\out"}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#ring@0.17.14","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\ring-0.17.14\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"ring","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\ring-0.17.14\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","default","dev_urandom_fallback"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libring-2e2ed4739d17e023.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#immutable-chunkmap@2.1.2","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\immutable-chunkmap-2.1.2\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"immutable_chunkmap","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\immutable-chunkmap-2.1.2\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libimmutable_chunkmap-a4d346a1b8c45aec.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#miniz_oxide@0.8.9","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\miniz_oxide-0.8.9\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"miniz_oxide","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\miniz_oxide-0.8.9\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","simd","simd-adler32","with-alloc"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libminiz_oxide-b520ddec9cfd8004.rmeta"],"executable":null,"fresh":true}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#generic-array@0.14.7","linked_libs":[],"linked_paths":[],"cfgs":["relaxed_coherence"],"env":[],"out_dir":"C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\generic-array-c78ec24c25db677f\\out"}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#serde@1.0.228","linked_libs":[],"linked_paths":[],"cfgs":["if_docsrs_then_no_serde_core"],"env":[],"out_dir":"C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\serde-05bd502ad2228cd6\\out"}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#httparse@1.10.1","linked_libs":[],"linked_paths":[],"cfgs":["httparse_simd_neon_intrinsics","httparse_simd"],"env":[],"out_dir":"C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\httparse-8cfa30df13af618b\\out"}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#windows_x86_64_msvc@0.48.5","linked_libs":[],"linked_paths":["native=C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows_x86_64_msvc-0.48.5\\lib"],"cfgs":[],"env":[],"out_dir":"C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\windows_x86_64_msvc-f454ad5cd31e8874\\out"}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#epaint@0.29.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\epaint-0.29.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"epaint","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\epaint-0.29.1\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["bytemuck","default_fonts","epaint_default_fonts","log"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libepaint-b0e16ac79584e03b.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#windows-core@0.58.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows-core-0.58.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"windows_core","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows-core-0.58.0\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libwindows_core-701b0b66dc3bc647.rmeta"],"executable":null,"fresh":true}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#winit@0.30.12","linked_libs":[],"linked_paths":[],"cfgs":["windows_platform"],"env":[],"out_dir":"C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\winit-0d3785c615a2560a\\out"}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#typenum@1.19.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\typenum-1.19.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"typenum","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\typenum-1.19.0\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libtypenum-f31514ac03be8a48.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#tinystr@0.8.2","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tinystr-0.8.2\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"tinystr","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tinystr-0.8.2\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["zerovec"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libtinystr-09a5d30393259d9b.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#potential_utf@0.1.4","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\potential_utf-0.1.4\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"potential_utf","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\potential_utf-0.1.4\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["zerovec"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libpotential_utf-edf62a521e21c202.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#futures-util@0.3.31","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\futures-util-0.3.31\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"futures_util","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\futures-util-0.3.31\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","futures-io","futures-sink","io","memchr","sink","slab","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libfutures_util-1500a0b49c6db20e.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#phf_shared@0.11.3","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\phf_shared-0.11.3\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"phf_shared","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\phf_shared-0.11.3\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std","unicase"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libphf_shared-73d3f056d2523cce.rlib","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libphf_shared-73d3f056d2523cce.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#num-traits@0.2.19","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\num-traits-0.2.19\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"num_traits","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\num-traits-0.2.19\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libnum_traits-2bff7112712dc5d0.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#glutin_wgl_sys@0.6.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\glutin_wgl_sys-0.6.1\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\glutin_wgl_sys-0.6.1\\build.rs","edition":"2021","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\glutin_wgl_sys-2fb75530339736f9\\build-script-build.exe","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\glutin_wgl_sys-2fb75530339736f9\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#glutin_egl_sys@0.7.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\glutin_egl_sys-0.7.1\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\glutin_egl_sys-0.7.1\\build.rs","edition":"2021","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\glutin_egl_sys-f203dfd8e2641cb7\\build-script-build.exe","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\glutin_egl_sys-f203dfd8e2641cb7\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#crc32fast@1.5.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\crc32fast-1.5.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"crc32fast","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\crc32fast-1.5.0\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libcrc32fast-09f10ff88ca8cd6b.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#serde_core@1.0.228","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\serde_core-1.0.228\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"serde_core","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\serde_core-1.0.228\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["result","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libserde_core-4188d55994d2dca2.rlib","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libserde_core-4188d55994d2dca2.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#try-lock@0.2.5","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\try-lock-0.2.5\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"try_lock","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\try-lock-0.2.5\\src\\lib.rs","edition":"2015","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libtry_lock-3c05ca19c877fec8.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#dpi@0.1.2","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\dpi-0.1.2\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"dpi","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\dpi-0.1.2\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libdpi-9f762cbdb2bb9d8e.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#smol_str@0.2.2","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\smol_str-0.2.2\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"smol_str","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\smol_str-0.2.2\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libsmol_str-27dff297d09eca46.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#rustls@0.23.35","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\rustls-0.23.35\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\rustls-0.23.35\\build.rs","edition":"2021","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["ring","std","tls12"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\rustls-7ddf8728d5d3371a\\build-script-build.exe","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\rustls-7ddf8728d5d3371a\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#libc@0.2.178","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\libc-0.2.178\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\libc-0.2.178\\build.rs","edition":"2021","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\libc-463b654b9a080f7d\\build-script-build.exe","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\libc-463b654b9a080f7d\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#cursor-icon@1.2.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\cursor-icon-1.2.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"cursor_icon","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\cursor-icon-1.2.0\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","default","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libcursor_icon-a8f148c05ac66d40.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#unicode-segmentation@1.12.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\unicode-segmentation-1.12.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"unicode_segmentation","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\unicode-segmentation-1.12.0\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libunicode_segmentation-727c7899e60e7449.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#icu_locale_core@2.1.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\icu_locale_core-2.1.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"icu_locale_core","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\icu_locale_core-2.1.1\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["zerovec"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libicu_locale_core-ba51ee7db6b2c568.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#icu_collections@2.1.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\icu_collections-2.1.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"icu_collections","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\icu_collections-2.1.1\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libicu_collections-dbf861d53bfd5e8d.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#tower-service@0.3.3","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tower-service-0.3.3\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"tower_service","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tower-service-0.3.3\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libtower_service-74900c90b6b64b60.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#want@0.3.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\want-0.3.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"want","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\want-0.3.1\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libwant-a5154952c149b38e.rmeta"],"executable":null,"fresh":true}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#rustls@0.23.35","linked_libs":[],"linked_paths":[],"cfgs":[],"env":[],"out_dir":"C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\rustls-9c84802de594c5f1\\out"}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#pxfm@0.1.27","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pxfm-0.1.27\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"pxfm","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pxfm-0.1.27\\src\\lib.rs","edition":"2024","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libpxfm-3c0d17eb86e51e62.rmeta"],"executable":null,"fresh":true}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#libc@0.2.178","linked_libs":[],"linked_paths":[],"cfgs":["freebsd12"],"env":[],"out_dir":"C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\libc-dc8ccef72c7d7271\\out"}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#glutin_wgl_sys@0.6.1","linked_libs":[],"linked_paths":[],"cfgs":[],"env":[],"out_dir":"C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\glutin_wgl_sys-c1ae307907271d5a\\out"}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#glutin_egl_sys@0.7.1","linked_libs":[],"linked_paths":[],"cfgs":[],"env":[],"out_dir":"C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\glutin_egl_sys-0e3ec7f5ee1e1503\\out"}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#winit@0.30.12","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\winit-0.30.12\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"winit","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\winit-0.30.12\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["ahash","bytemuck","default","memmap2","percent-encoding","rwh_06","sctk","sctk-adwaita","wayland","wayland-backend","wayland-client","wayland-csd-adwaita","wayland-dlopen","wayland-protocols","wayland-protocols-plasma","x11","x11-dl","x11rb"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libwinit-eac0a670876d2505.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#flate2@1.1.5","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\flate2-1.1.5\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"flate2","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\flate2-1.1.5\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["any_impl","default","miniz_oxide","rust_backend"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libflate2-f37162116f5c6ede.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#serde@1.0.228","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\serde-1.0.228\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"serde","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\serde-1.0.228\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libserde-9a9dcffbc851fb6a.rlib","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libserde-9a9dcffbc851fb6a.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#phf@0.11.3","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\phf-0.11.3\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"phf","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\phf-0.11.3\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std","unicase"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libphf-b7ab041ad0a664ab.rlib","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libphf-b7ab041ad0a664ab.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#paste@1.0.15","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\paste-1.0.15\\Cargo.toml","target":{"kind":["proc-macro"],"crate_types":["proc-macro"],"name":"paste","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\paste-1.0.15\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\paste-0695c518049cc4ea.dll","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\paste-0695c518049cc4ea.dll.lib","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\paste-0695c518049cc4ea.dll.exp","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\paste-0695c518049cc4ea.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#rustls-webpki@0.103.8","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\rustls-webpki-0.103.8\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"webpki","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\rustls-webpki-0.103.8\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","ring","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libwebpki-9cb82be595f64392.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#windows@0.58.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows-0.58.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"windows","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows-0.58.0\\src\\lib.rs","edition":"2021","doc":true,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["Win32","Win32_Foundation","Win32_Graphics","Win32_Graphics_Gdi","Win32_Security","Win32_Storage","Win32_Storage_FileSystem","Win32_System","Win32_System_Com","Win32_System_Diagnostics","Win32_System_Diagnostics_Debug","Win32_System_Diagnostics_ToolHelp","Win32_System_LibraryLoader","Win32_System_Memory","Win32_System_Ole","Win32_System_ProcessStatus","Win32_System_Registry","Win32_System_SystemInformation","Win32_System_Threading","Win32_System_Variant","Win32_UI","Win32_UI_Accessibility","Win32_UI_Input","Win32_UI_Input_KeyboardAndMouse","Win32_UI_WindowsAndMessaging","default","implement","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libwindows-316d1a3f0ee3c03e.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#icu_provider@2.1.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\icu_provider-2.1.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"icu_provider","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\icu_provider-2.1.1\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["baked"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libicu_provider-102474d7564b1b4e.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#egui@0.29.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\egui-0.29.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"egui","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\egui-0.29.1\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["accesskit","bytemuck","default","default_fonts","log"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libegui-cd693acb0b34b6db.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#generic-array@0.14.7","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\generic-array-0.14.7\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"generic_array","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\generic-array-0.14.7\\src\\lib.rs","edition":"2015","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["more_lengths"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libgeneric_array-2ed2834863c2ec3f.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#httparse@1.10.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\httparse-1.10.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"httparse","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\httparse-1.10.1\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libhttparse-8e72a2100eef0f6b.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#accesskit_consumer@0.24.3","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\accesskit_consumer-0.24.3\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"accesskit_consumer","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\accesskit_consumer-0.24.3\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libaccesskit_consumer-67a3f33ee6501ed7.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#windows_x86_64_msvc@0.48.5","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows_x86_64_msvc-0.48.5\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"windows_x86_64_msvc","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows_x86_64_msvc-0.48.5\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libwindows_x86_64_msvc-f441f67a269db201.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#fdeflate@0.3.7","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\fdeflate-0.3.7\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"fdeflate","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\fdeflate-0.3.7\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libfdeflate-ea06caf7fc75112a.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#futures-channel@0.3.31","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\futures-channel-0.3.31\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"futures_channel","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\futures-channel-0.3.31\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","default","futures-sink","sink","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libfutures_channel-487bb17f19e51b6a.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#memoffset@0.9.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\memoffset-0.9.1\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\memoffset-0.9.1\\build.rs","edition":"2015","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\memoffset-81fbeea12c5faa32\\build-script-build.exe","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\memoffset-81fbeea12c5faa32\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#glutin@0.32.3","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\glutin-0.32.3\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\glutin-0.32.3\\build.rs","edition":"2021","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","egl","glutin_egl_sys","glutin_glx_sys","glutin_wgl_sys","glx","libloading","wayland","wayland-sys","wgl","windows-sys","x11","x11-dl"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\glutin-d34cfca3a3336fc9\\build-script-build.exe","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\glutin-d34cfca3a3336fc9\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#subtle@2.6.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\subtle-2.6.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"subtle","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\subtle-2.6.1\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libsubtle-61807d6a4ba84f63.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#error-code@3.3.2","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\error-code-3.3.2\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"error_code","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\error-code-3.3.2\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\liberror_code-a781eb1128e4d492.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#atomic-waker@1.1.2","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\atomic-waker-1.1.2\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"atomic_waker","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\atomic-waker-1.1.2\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libatomic_waker-7d468e857fd11632.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#adler2@2.0.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\adler2-2.0.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"adler2","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\adler2-2.0.1\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libadler2-39385ce5c7123f0f.rlib","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libadler2-39385ce5c7123f0f.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#static_assertions@1.1.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\static_assertions-1.1.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"static_assertions","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\static_assertions-1.1.0\\src\\lib.rs","edition":"2015","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libstatic_assertions-11fb59210ddc08da.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#simd-adler32@0.3.8","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\simd-adler32-0.3.8\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"simd_adler32","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\simd-adler32-0.3.8\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libsimd_adler32-ceb494831f475609.rlib","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libsimd_adler32-ceb494831f475609.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#icu_normalizer@2.1.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\icu_normalizer-2.1.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"icu_normalizer","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\icu_normalizer-2.1.1\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["compiled_data"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libicu_normalizer-4130853fad0721ad.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#icu_properties@2.1.2","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\icu_properties-2.1.2\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"icu_properties","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\icu_properties-2.1.2\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["compiled_data"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libicu_properties-57d2f8bd984e2811.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#cfg-if@1.0.4","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\cfg-if-1.0.4\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"cfg_if","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\cfg-if-1.0.4\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libcfg_if-88c2812e41664377.rlib","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libcfg_if-88c2812e41664377.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#accesskit_windows@0.23.2","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\accesskit_windows-0.23.2\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"accesskit_windows","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\accesskit_windows-0.23.2\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libaccesskit_windows-5c7d4afecbbf1aef.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#rustls@0.23.35","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\rustls-0.23.35\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"rustls","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\rustls-0.23.35\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["ring","std","tls12"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\librustls-02fd750ea96b378d.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#clipboard-win@5.4.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\clipboard-win-5.4.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"clipboard_win","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\clipboard-win-5.4.1\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libclipboard_win-679e36801d54ff63.rmeta"],"executable":null,"fresh":true}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#glutin@0.32.3","linked_libs":[],"linked_paths":[],"cfgs":["egl_backend","wgl_backend"],"env":[],"out_dir":"C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\glutin-ea0033e176b4f57a\\out"}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#png@0.18.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\png-0.18.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"png","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\png-0.18.0\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libpng-6467bff7162c098e.rmeta"],"executable":null,"fresh":true}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#memoffset@0.9.1","linked_libs":[],"linked_paths":[],"cfgs":["tuple_ty","allow_clippy","maybe_uninit","doctests","raw_ref_macros","stable_const","stable_offset_of"],"env":[],"out_dir":"C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\memoffset-48913e325f54e736\\out"}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#hyper@1.8.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\hyper-1.8.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"hyper","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\hyper-1.8.1\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["client","default","http1"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libhyper-76ebca0ca6e6fd97.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#windows-targets@0.48.5","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows-targets-0.48.5\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"windows_targets","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows-targets-0.48.5\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libwindows_targets-969c488cf07c6026.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#miniz_oxide@0.8.9","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\miniz_oxide-0.8.9\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"miniz_oxide","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\miniz_oxide-0.8.9\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["simd","simd-adler32","with-alloc"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libminiz_oxide-2af8080a48ef96be.rlib","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libminiz_oxide-2af8080a48ef96be.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#mime_guess2@2.3.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\mime_guess2-2.3.1\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\mime_guess2-2.3.1\\build.rs","edition":"2024","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\mime_guess2-d9bcd66d3d473bd6\\build-script-build.exe","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\mime_guess2-d9bcd66d3d473bd6\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#moxcms@0.7.11","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\moxcms-0.7.11\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"moxcms","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\moxcms-0.7.11\\src\\lib.rs","edition":"2024","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["avx","default","neon","sse"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libmoxcms-88791ecb5c051849.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#libc@0.2.178","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\libc-0.2.178\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"libc","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\libc-0.2.178\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\liblibc-3f558fc9e42e1c32.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#toml@0.5.11","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\toml-0.5.11\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"toml","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\toml-0.5.11\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libtoml-c64349b45b77d670.rlib","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libtoml-c64349b45b77d670.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#idna_adapter@1.2.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\idna_adapter-1.2.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"idna_adapter","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\idna_adapter-1.2.1\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["compiled_data"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libidna_adapter-74d37f149732e5f6.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#crc32fast@1.5.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\crc32fast-1.5.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"crc32fast","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\crc32fast-1.5.0\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libcrc32fast-080e751d5ea5b265.rlib","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libcrc32fast-080e751d5ea5b265.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#glutin_wgl_sys@0.6.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\glutin_wgl_sys-0.6.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"glutin_wgl_sys","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\glutin_wgl_sys-0.6.1\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libglutin_wgl_sys-b046279d379adb5e.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#glutin_egl_sys@0.7.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\glutin_egl_sys-0.7.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"glutin_egl_sys","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\glutin_egl_sys-0.7.1\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libglutin_egl_sys-2987673e41e26e92.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#sync_wrapper@1.0.2","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sync_wrapper-1.0.2\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"sync_wrapper","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sync_wrapper-1.0.2\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["futures","futures-core"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libsync_wrapper-eea9f3656f3394b5.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#glutin-winit@0.5.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\glutin-winit-0.5.0\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\glutin-winit-0.5.0\\build.rs","edition":"2021","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","egl","glx","wayland","wgl","x11"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\glutin-winit-998ba8811e5dc512\\build-script-build.exe","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\glutin-winit-998ba8811e5dc512\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#libloading@0.8.9","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\libloading-0.8.9\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"libloading","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\libloading-0.8.9\\src\\lib.rs","edition":"2015","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\liblibloading-d8655b2bac2db3b3.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#ryu@1.0.21","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\ryu-1.0.21\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"ryu","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\ryu-1.0.21\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libryu-fd617149a3e332d1.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#byteorder-lite@0.1.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\byteorder-lite-0.1.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"byteorder_lite","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\byteorder-lite-0.1.0\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libbyteorder_lite-00f1a1c27d089663.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#ipnet@2.11.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\ipnet-2.11.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"ipnet","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\ipnet-2.11.0\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libipnet-f7cf0f5754b610a3.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#winapi@0.3.9","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\winapi-0.3.9\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\winapi-0.3.9\\build.rs","edition":"2015","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["winuser"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\winapi-31e6040f13ec9596\\build-script-build.exe","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\winapi-31e6040f13ec9596\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#tower-layer@0.3.3","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tower-layer-0.3.3\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"tower_layer","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tower-layer-0.3.3\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libtower_layer-e7e2ef93d87d0258.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#serde_json@1.0.145","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\serde_json-1.0.145\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\serde_json-1.0.145\\build.rs","edition":"2021","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\serde_json-7b77d7d2b5ea0a52\\build-script-build.exe","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\serde_json-7b77d7d2b5ea0a52\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#base64@0.22.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\base64-0.22.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"base64","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\base64-0.22.1\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","default","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libbase64-abbb6c0fe31e8c8e.rmeta"],"executable":null,"fresh":true}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#mime_guess2@2.3.1","linked_libs":[],"linked_paths":[],"cfgs":[],"env":[["MIME_TYPES_GENERATED_PATH","\\mime_types_generated.rs"]],"out_dir":"C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\mime_guess2-44e945d73ed522f9\\out"}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#memoffset@0.9.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\memoffset-0.9.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"memoffset","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\memoffset-0.9.1\\src\\lib.rs","edition":"2015","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libmemoffset-8cad737f670d5ef2.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#idna@1.1.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\idna-1.1.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"idna","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\idna-1.1.0\\src\\lib.rs","edition":"2018","doc":true,"doctest":false,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","compiled_data","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libidna-d53b23d29c7bacb5.rmeta"],"executable":null,"fresh":true}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#winapi@0.3.9","linked_libs":["dylib=gdi32","dylib=kernel32","dylib=msimg32","dylib=opengl32","dylib=user32","dylib=winspool"],"linked_paths":[],"cfgs":["feature=\"minwindef\"","feature=\"ntstatus\"","feature=\"guiddef\"","feature=\"limits\"","feature=\"vcruntime\"","feature=\"wingdi\"","feature=\"ktmtypes\"","feature=\"basetsd\"","feature=\"windef\"","feature=\"vadefs\"","feature=\"minwinbase\"","feature=\"winnt\"","feature=\"ntdef\"","feature=\"excpt\""],"env":[],"out_dir":"C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\winapi-600313191d842343\\out"}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#glutin-winit@0.5.0","linked_libs":[],"linked_paths":[],"cfgs":["egl_backend","wgl_backend"],"env":[],"out_dir":"C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\glutin-winit-6c6aa5cebbd5a041\\out"}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#tower@0.5.2","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tower-0.5.2\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"tower","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tower-0.5.2\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["__common","futures-core","futures-util","pin-project-lite","retry","sync_wrapper","timeout","tokio","util"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libtower-e90c8600c5dd3b1a.rmeta"],"executable":null,"fresh":true}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#serde_json@1.0.145","linked_libs":[],"linked_paths":[],"cfgs":["fast_arithmetic=\"64\""],"env":[],"out_dir":"C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\serde_json-7becdae10421fbaf\\out"}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#image@0.25.9","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\image-0.25.9\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"image","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\image-0.25.9\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["png"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libimage-8f05e38b8c5ab0c9.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#glutin@0.32.3","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\glutin-0.32.3\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"glutin","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\glutin-0.32.3\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","egl","glutin_egl_sys","glutin_glx_sys","glutin_wgl_sys","glx","libloading","wayland","wayland-sys","wgl","windows-sys","x11","x11-dl"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libglutin-9b1da7186c62a6e9.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#flate2@1.1.5","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\flate2-1.1.5\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"flate2","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\flate2-1.1.5\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["any_impl","miniz_oxide","rust_backend"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libflate2-3e29ecc62a79d345.rlib","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libflate2-3e29ecc62a79d345.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#hyper-util@0.1.19","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\hyper-util-0.1.19\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"hyper_util","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\hyper-util-0.1.19\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["client","client-legacy","client-proxy","default","http1","tokio"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libhyper_util-c69341dc944760f8.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#windows-sys@0.48.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows-sys-0.48.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"windows_sys","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows-sys-0.48.0\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["Win32","Win32_Foundation","Win32_Security","Win32_Storage","Win32_Storage_FileSystem","Win32_System","Win32_System_Com","Win32_System_Diagnostics","Win32_System_Diagnostics_Debug","Win32_System_Registry","Win32_System_Time","Win32_UI","Win32_UI_Shell","Win32_UI_Shell_Common","Win32_UI_WindowsAndMessaging","default"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libwindows_sys-04dc6c7c38743890.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#accesskit_winit@0.22.4","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\accesskit_winit-0.22.4\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"accesskit_winit","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\accesskit_winit-0.22.4\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["accesskit_unix","async-io","default","rwh_06"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libaccesskit_winit-5877cf16eb996910.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#arboard@3.6.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\arboard-3.6.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"arboard","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\arboard-3.6.1\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libarboard-eac9e85794b27505.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#tokio-rustls@0.26.4","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tokio-rustls-0.26.4\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"tokio_rustls","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tokio-rustls-0.26.4\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["ring","tls12"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libtokio_rustls-a25b62d5f7f64481.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#winres@0.1.12","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\winres-0.1.12\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"winres","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\winres-0.1.12\\lib.rs","edition":"2015","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libwinres-2de997fb03a936aa.rlib","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libwinres-2de997fb03a936aa.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#crypto-common@0.1.7","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\crypto-common-0.1.7\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"crypto_common","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\crypto-common-0.1.7\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libcrypto_common-44ee574612d41dbf.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#block-buffer@0.10.4","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\block-buffer-0.10.4\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"block_buffer","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\block-buffer-0.10.4\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libblock_buffer-91a64b050109d3aa.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#url@2.5.7","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\url-2.5.7\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"url","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\url-2.5.7\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","serde","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\liburl-0614ec92eb7e7081.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#webpki-roots@1.0.4","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\webpki-roots-1.0.4\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"webpki_roots","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\webpki-roots-1.0.4\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libwebpki_roots-9a92ada2ac916464.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#enum-map-derive@0.17.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\enum-map-derive-0.17.0\\Cargo.toml","target":{"kind":["proc-macro"],"crate_types":["proc-macro"],"name":"enum_map_derive","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\enum-map-derive-0.17.0\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\enum_map_derive-f93f312610944a30.dll","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\enum_map_derive-f93f312610944a30.dll.lib","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\enum_map_derive-f93f312610944a30.dll.exp","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\enum_map_derive-f93f312610944a30.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#glow@0.14.2","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\glow-0.14.2\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"glow","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\glow-0.14.2\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libglow-f83eebaf5be1e70a.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#mime@0.3.17","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\mime-0.3.17\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"mime","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\mime-0.3.17\\src\\lib.rs","edition":"2015","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libmime-1f02be28a2d040c5.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#iri-string@0.7.9","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\iri-string-0.7.9\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"iri_string","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\iri-string-0.7.9\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","default","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libiri_string-cfd9c5230b64b76e.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#rfd@0.14.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\rfd-0.14.1\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\rfd-0.14.1\\build.rs","edition":"2021","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["ashpd","async-std","default","pollster","urlencoding","xdg-portal"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\rfd-31b44e45c599f292\\build-script-build.exe","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\rfd-31b44e45c599f292\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#web-time@1.1.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\web-time-1.1.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"web_time","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\web-time-1.1.0\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libweb_time-d516c565748f3a04.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#thiserror@1.0.69","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\thiserror-1.0.69\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\thiserror-1.0.69\\build.rs","edition":"2021","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\thiserror-afffb955d5ace971\\build-script-build.exe","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\thiserror-afffb955d5ace971\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#anyhow@1.0.100","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\anyhow-1.0.100\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\anyhow-1.0.100\\build.rs","edition":"2018","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\anyhow-0aa8210a5db639a8\\build-script-build.exe","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\anyhow-0aa8210a5db639a8\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#unicase@2.8.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\unicase-2.8.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"unicase","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\unicase-2.8.1\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libunicase-a84622ad4e5b279d.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#litrs@1.0.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\litrs-1.0.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"litrs","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\litrs-1.0.0\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\liblitrs-093c1a227e5ef44b.rlib","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\liblitrs-093c1a227e5ef44b.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#glutin-winit@0.5.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\glutin-winit-0.5.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"glutin_winit","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\glutin-winit-0.5.0\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","egl","glx","wayland","wgl","x11"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libglutin_winit-4e8fa5730f3642a7.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#serde_json@1.0.145","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\serde_json-1.0.145\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"serde_json","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\serde_json-1.0.145\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libserde_json-fbf307ed44b55eb1.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"path+file:///C:/Users/<USER>/Downloads/CS2/lux-launcher-rs#lux-launcher@1.0.0","manifest_path":"C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\build.rs","edition":"2021","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","embed_dll"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\lux-launcher-9d7974d805089b25\\build-script-build.exe","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\lux-launcher-9d7974d805089b25\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#winapi@0.3.9","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\winapi-0.3.9\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"winapi","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\winapi-0.3.9\\src\\lib.rs","edition":"2015","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["winuser"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libwinapi-6144992a5f620421.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#webbrowser@1.0.6","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\webbrowser-1.0.6\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"webbrowser","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\webbrowser-1.0.6\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libwebbrowser-fbfdad2365e2cc39.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#enum-map@2.7.3","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\enum-map-2.7.3\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"enum_map","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\enum-map-2.7.3\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["serde"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libenum_map-09b519d1bd855cc4.rmeta"],"executable":null,"fresh":true}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#anyhow@1.0.100","linked_libs":[],"linked_paths":[],"cfgs":["std_backtrace"],"env":[],"out_dir":"C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\anyhow-89bc24647526a216\\out"}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#rfd@0.14.1","linked_libs":[],"linked_paths":[],"cfgs":[],"env":[],"out_dir":"C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\rfd-1b4abe6ef5f59336\\out"}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#egui_glow@0.29.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\egui_glow-0.29.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"egui_glow","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\egui_glow-0.29.1\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["wayland","x11"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libegui_glow-c5dac8a35ae049de.rmeta"],"executable":null,"fresh":true}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#thiserror@1.0.69","linked_libs":[],"linked_paths":[],"cfgs":[],"env":[],"out_dir":"C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\thiserror-9b5ccfa5a38f3754\\out"}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#document-features@0.2.12","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\document-features-0.2.12\\Cargo.toml","target":{"kind":["proc-macro"],"crate_types":["proc-macro"],"name":"document_features","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\document-features-0.2.12\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\document_features-d728630f0af2aaf7.dll","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\document_features-d728630f0af2aaf7.dll.lib","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\document_features-d728630f0af2aaf7.dll.exp","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\document_features-d728630f0af2aaf7.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#mime_guess2@2.3.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\mime_guess2-2.3.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"mime_guess2","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\mime_guess2-2.3.1\\src\\lib.rs","edition":"2024","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libmime_guess2-9a58eb2ceb097458.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#tower-http@0.6.8","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tower-http-0.6.8\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"tower_http","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tower-http-0.6.8\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["follow-redirect","futures-util","iri-string","tower"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libtower_http-15e9d22ef2fa6e67.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#hyper-rustls@0.27.7","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\hyper-rustls-0.27.7\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"hyper_rustls","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\hyper-rustls-0.27.7\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["http1","ring","tls12","webpki-roots","webpki-tokio"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libhyper_rustls-85c82dd9998d960e.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#digest@0.10.7","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\digest-0.10.7\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"digest","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\digest-0.10.7\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","block-buffer","core-api","default","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libdigest-f1b40b2b77647d74.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#serde_urlencoded@0.7.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\serde_urlencoded-0.7.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"serde_urlencoded","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\serde_urlencoded-0.7.1\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":false},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libserde_urlencoded-9126ad95acda74be.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#http-body-util@0.1.3","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\http-body-util-0.1.3\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"http_body_util","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\http-body-util-0.1.3\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libhttp_body_util-0feb394ed4d723c3.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#thiserror-impl@1.0.69","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\thiserror-impl-1.0.69\\Cargo.toml","target":{"kind":["proc-macro"],"crate_types":["proc-macro"],"name":"thiserror_impl","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\thiserror-impl-1.0.69\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\thiserror_impl-b6b61bb4fd72ec22.dll","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\thiserror_impl-b6b61bb4fd72ec22.dll.lib","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\thiserror_impl-b6b61bb4fd72ec22.dll.exp","C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\thiserror_impl-b6b61bb4fd72ec22.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#cpufeatures@0.2.17","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\cpufeatures-0.2.17\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"cpufeatures","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\cpufeatures-0.2.17\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libcpufeatures-bdf8e86a231c4d5b.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#egui-winit@0.29.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\egui-winit-0.29.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"egui_winit","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\egui-winit-0.29.1\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["accesskit","accesskit_winit","arboard","bytemuck","clipboard","links","smithay-clipboard","wayland","webbrowser","x11"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libegui_winit-aaabf4f043938cc3.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#sha2@0.10.9","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sha2-0.10.9\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"sha2","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sha2-0.10.9\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libsha2-35f74edd8a361e7b.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#egui_extras@0.29.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\egui_extras-0.29.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"egui_extras","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\egui_extras-0.29.1\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","image"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libegui_extras-09f9bc6984c6b22f.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#thiserror@1.0.69","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\thiserror-1.0.69\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"thiserror","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\thiserror-1.0.69\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libthiserror-c48ad90e6e0b9d56.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#reqwest@0.12.26","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\reqwest-0.12.26\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"reqwest","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\reqwest-0.12.26\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["__rustls","__rustls-ring","__tls","blocking","json","rustls-tls","rustls-tls-webpki-roots","rustls-tls-webpki-roots-no-provider"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libreqwest-7ca5bd4372436cd8.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#rfd@0.14.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\rfd-0.14.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"rfd","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\rfd-0.14.1\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["ashpd","async-std","default","pollster","urlencoding","xdg-portal"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\librfd-cac1a5239c2eab9f.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#anyhow@1.0.100","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\anyhow-1.0.100\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"anyhow","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\anyhow-1.0.100\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libanyhow-fbb20bfd1a100f6f.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#winreg@0.52.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\winreg-0.52.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"winreg","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\winreg-0.52.0\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libwinreg-38d22ed7ab35ed4a.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#raw-cpuid@11.6.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\raw-cpuid-11.6.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"raw_cpuid","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\raw-cpuid-11.6.0\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libraw_cpuid-2ddb8d58cff94df7.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#base64@0.21.7","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\base64-0.21.7\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"base64","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\base64-0.21.7\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","default","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libbase64-7734bfa11ed5b63d.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#hex@0.4.3","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\hex-0.4.3\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"hex","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\hex-0.4.3\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","default","std"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libhex-f7264a1a99e611f2.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#obfstr@0.4.4","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\obfstr-0.4.4\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"obfstr","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\obfstr-0.4.4\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libobfstr-8b5ebc784a628160.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#eframe@0.29.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\eframe-0.29.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"eframe","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\eframe-0.29.1\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["accesskit","default","default_fonts","glow","wayland","web_screen_reader","x11"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\libeframe-71b2d21b594cf5ed.rmeta"],"executable":null,"fresh":true}
{"reason":"build-script-executed","package_id":"path+file:///C:/Users/<USER>/Downloads/CS2/lux-launcher-rs#lux-launcher@1.0.0","linked_libs":["dylib=resource"],"linked_paths":["native=C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\lux-launcher-2779aae46ada43f7\\out"],"cfgs":[],"env":[],"out_dir":"C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\build\\lux-launcher-2779aae46ada43f7\\out"}
{"reason":"compiler-artifact","package_id":"path+file:///C:/Users/<USER>/Downloads/CS2/lux-launcher-rs#lux-launcher@1.0.0","manifest_path":"C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\Cargo.toml","target":{"kind":["bin"],"crate_types":["bin"],"name":"lux-launcher","src_path":"C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\src\\main.rs","edition":"2021","doc":true,"doctest":false,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","embed_dll"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\liblux_launcher-1ff9fa30ac1b8b51.rmeta"],"executable":null,"fresh":false}
{"reason":"compiler-artifact","package_id":"path+file:///C:/Users/<USER>/Downloads/CS2/lux-launcher-rs#lux-launcher@1.0.0","manifest_path":"C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\Cargo.toml","target":{"kind":["bin"],"crate_types":["bin"],"name":"lux-launcher","src_path":"C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\src\\main.rs","edition":"2021","doc":true,"doctest":false,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":true},"features":["default","embed_dll"],"filenames":["C:\\Users\\<USER>\\Downloads\\CS2\\lux-launcher-rs\\target\\debug\\deps\\liblux_launcher-65dc7742d0d1d5be.rmeta"],"executable":null,"fresh":false}
{"reason":"build-finished","success":true}
