﻿  Assembling src\memory\RetSpoofStub.asm...
  imgui_stdlib.cpp
  imgui.cpp
  imgui_draw.cpp
  imgui_freetype.cpp
  imgui_tables.cpp
  imgui_widgets.cpp
  imgui_impl_dx11.cpp
  imgui_impl_win32.cpp
  lodePNG.cpp
  Config.cpp
  Download.cpp
  Features.cpp
  Legitbot.cpp
  weapon_config.cpp
  accuracy_config.cpp
  damage_config.cpp
  ChatHandler.cpp
  Miscellaneous.cpp
  Movement.cpp
  ShotHandler.cpp
  Prediction.cpp
  Antiaim.cpp
  Autowall.cpp
  Doubletap.cpp
  Lagcomp.cpp
  NoSpread.cpp
  Ragebot.cpp
  Chams.cpp
  Grenade.cpp
  GrenadePrediction.cpp
  Inventory.cpp
  Modulation.cpp
  Overlay.cpp
  ParticleManager.cpp
  PlayerESP.cpp
  Scope.cpp
  Globals.cpp
  ApraGui.cpp
  DX11BlurEffect.cpp
  FontAwesome.cpp
  Gui.cpp
  Helpers.cpp
  Hooks.cpp
  Main.cpp
  CSHA1.cpp
  MurMur.cpp
  CBaseHandle.cpp
  Vector.cpp
  Memory.cpp
  Draw.cpp
  InputSystem.cpp
  Logging.cpp
  Math.cpp
  ResourceManager.cpp
  Utilities.cpp
  Convars.cpp
  Entity.cpp
  CEventManager.cpp
  Interfaces.cpp
  CCSPlayerInventory.cpp
  CCSPlayerInventoryManager.cpp
  cs_usercmd.pb.cpp
  networkbasetypes.pb.cpp
  network_connection.pb.cpp
  usercmd.pb.cpp
  Schema.cpp
  Functions.cpp
  Trace.cpp
  buffer.c
  hde32.c
  hde64.c
  hook.c
  trampoline.c
freetype.lib(ftbase.obj) : warning LNK4099: PDB 'freetype.pdb' was not found with 'freetype.lib(ftbase.obj)' or at 'C:\Users\<USER>\Downloads\CS2\sensical_cs2-master\build\x64\Release\freetype.pdb'; linking object as if no debug info
freetype.lib(ftinit.obj) : warning LNK4099: PDB 'freetype.pdb' was not found with 'freetype.lib(ftinit.obj)' or at 'C:\Users\<USER>\Downloads\CS2\sensical_cs2-master\build\x64\Release\freetype.pdb'; linking object as if no debug info
freetype.lib(ftsynth.obj) : warning LNK4099: PDB 'freetype.pdb' was not found with 'freetype.lib(ftsynth.obj)' or at 'C:\Users\<USER>\Downloads\CS2\sensical_cs2-master\build\x64\Release\freetype.pdb'; linking object as if no debug info
freetype.lib(ftsystem.obj) : warning LNK4099: PDB 'freetype.pdb' was not found with 'freetype.lib(ftsystem.obj)' or at 'C:\Users\<USER>\Downloads\CS2\sensical_cs2-master\build\x64\Release\freetype.pdb'; linking object as if no debug info
freetype.lib(autofit.obj) : warning LNK4099: PDB 'freetype.pdb' was not found with 'freetype.lib(autofit.obj)' or at 'C:\Users\<USER>\Downloads\CS2\sensical_cs2-master\build\x64\Release\freetype.pdb'; linking object as if no debug info
freetype.lib(truetype.obj) : warning LNK4099: PDB 'freetype.pdb' was not found with 'freetype.lib(truetype.obj)' or at 'C:\Users\<USER>\Downloads\CS2\sensical_cs2-master\build\x64\Release\freetype.pdb'; linking object as if no debug info
freetype.lib(type1.obj) : warning LNK4099: PDB 'freetype.pdb' was not found with 'freetype.lib(type1.obj)' or at 'C:\Users\<USER>\Downloads\CS2\sensical_cs2-master\build\x64\Release\freetype.pdb'; linking object as if no debug info
freetype.lib(cff.obj) : warning LNK4099: PDB 'freetype.pdb' was not found with 'freetype.lib(cff.obj)' or at 'C:\Users\<USER>\Downloads\CS2\sensical_cs2-master\build\x64\Release\freetype.pdb'; linking object as if no debug info
freetype.lib(type1cid.obj) : warning LNK4099: PDB 'freetype.pdb' was not found with 'freetype.lib(type1cid.obj)' or at 'C:\Users\<USER>\Downloads\CS2\sensical_cs2-master\build\x64\Release\freetype.pdb'; linking object as if no debug info
freetype.lib(pfr.obj) : warning LNK4099: PDB 'freetype.pdb' was not found with 'freetype.lib(pfr.obj)' or at 'C:\Users\<USER>\Downloads\CS2\sensical_cs2-master\build\x64\Release\freetype.pdb'; linking object as if no debug info
freetype.lib(type42.obj) : warning LNK4099: PDB 'freetype.pdb' was not found with 'freetype.lib(type42.obj)' or at 'C:\Users\<USER>\Downloads\CS2\sensical_cs2-master\build\x64\Release\freetype.pdb'; linking object as if no debug info
freetype.lib(winfnt.obj) : warning LNK4099: PDB 'freetype.pdb' was not found with 'freetype.lib(winfnt.obj)' or at 'C:\Users\<USER>\Downloads\CS2\sensical_cs2-master\build\x64\Release\freetype.pdb'; linking object as if no debug info
freetype.lib(pcf.obj) : warning LNK4099: PDB 'freetype.pdb' was not found with 'freetype.lib(pcf.obj)' or at 'C:\Users\<USER>\Downloads\CS2\sensical_cs2-master\build\x64\Release\freetype.pdb'; linking object as if no debug info
freetype.lib(bdf.obj) : warning LNK4099: PDB 'freetype.pdb' was not found with 'freetype.lib(bdf.obj)' or at 'C:\Users\<USER>\Downloads\CS2\sensical_cs2-master\build\x64\Release\freetype.pdb'; linking object as if no debug info
freetype.lib(psaux.obj) : warning LNK4099: PDB 'freetype.pdb' was not found with 'freetype.lib(psaux.obj)' or at 'C:\Users\<USER>\Downloads\CS2\sensical_cs2-master\build\x64\Release\freetype.pdb'; linking object as if no debug info
freetype.lib(psmodule.obj) : warning LNK4099: PDB 'freetype.pdb' was not found with 'freetype.lib(psmodule.obj)' or at 'C:\Users\<USER>\Downloads\CS2\sensical_cs2-master\build\x64\Release\freetype.pdb'; linking object as if no debug info
freetype.lib(pshinter.obj) : warning LNK4099: PDB 'freetype.pdb' was not found with 'freetype.lib(pshinter.obj)' or at 'C:\Users\<USER>\Downloads\CS2\sensical_cs2-master\build\x64\Release\freetype.pdb'; linking object as if no debug info
freetype.lib(sfnt.obj) : warning LNK4099: PDB 'freetype.pdb' was not found with 'freetype.lib(sfnt.obj)' or at 'C:\Users\<USER>\Downloads\CS2\sensical_cs2-master\build\x64\Release\freetype.pdb'; linking object as if no debug info
freetype.lib(smooth.obj) : warning LNK4099: PDB 'freetype.pdb' was not found with 'freetype.lib(smooth.obj)' or at 'C:\Users\<USER>\Downloads\CS2\sensical_cs2-master\build\x64\Release\freetype.pdb'; linking object as if no debug info
freetype.lib(raster.obj) : warning LNK4099: PDB 'freetype.pdb' was not found with 'freetype.lib(raster.obj)' or at 'C:\Users\<USER>\Downloads\CS2\sensical_cs2-master\build\x64\Release\freetype.pdb'; linking object as if no debug info
freetype.lib(sdf.obj) : warning LNK4099: PDB 'freetype.pdb' was not found with 'freetype.lib(sdf.obj)' or at 'C:\Users\<USER>\Downloads\CS2\sensical_cs2-master\build\x64\Release\freetype.pdb'; linking object as if no debug info
freetype.lib(svg.obj) : warning LNK4099: PDB 'freetype.pdb' was not found with 'freetype.lib(svg.obj)' or at 'C:\Users\<USER>\Downloads\CS2\sensical_cs2-master\build\x64\Release\freetype.pdb'; linking object as if no debug info
freetype.lib(ftbitmap.obj) : warning LNK4099: PDB 'freetype.pdb' was not found with 'freetype.lib(ftbitmap.obj)' or at 'C:\Users\<USER>\Downloads\CS2\sensical_cs2-master\build\x64\Release\freetype.pdb'; linking object as if no debug info
freetype.lib(ftmm.obj) : warning LNK4099: PDB 'freetype.pdb' was not found with 'freetype.lib(ftmm.obj)' or at 'C:\Users\<USER>\Downloads\CS2\sensical_cs2-master\build\x64\Release\freetype.pdb'; linking object as if no debug info
freetype.lib(ftgzip.obj) : warning LNK4099: PDB 'freetype.pdb' was not found with 'freetype.lib(ftgzip.obj)' or at 'C:\Users\<USER>\Downloads\CS2\sensical_cs2-master\build\x64\Release\freetype.pdb'; linking object as if no debug info
freetype.lib(ftlzw.obj) : warning LNK4099: PDB 'freetype.pdb' was not found with 'freetype.lib(ftlzw.obj)' or at 'C:\Users\<USER>\Downloads\CS2\sensical_cs2-master\build\x64\Release\freetype.pdb'; linking object as if no debug info
freetype.lib(ftdebug.obj) : warning LNK4099: PDB 'freetype.pdb' was not found with 'freetype.lib(ftdebug.obj)' or at 'C:\Users\<USER>\Downloads\CS2\sensical_cs2-master\build\x64\Release\freetype.pdb'; linking object as if no debug info
LINK : warning LNK4098: defaultlib 'LIBCMT' conflicts with use of other libs; use /NODEFAULTLIB:library
  Counter-Strike2 Internal.vcxproj -> C:\Users\<USER>\Downloads\CS2\sensical_cs2-master\build\x64\Release\LUX-CS2.dll
