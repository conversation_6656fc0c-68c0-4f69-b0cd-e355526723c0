{"rustc": 17273738520181233363, "features": "[\"follow-redirect\", \"futures-util\", \"iri-string\", \"tower\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 17577061573142048237, "profile": 10004052855522644898, "path": 11368815164220839090, "deps": [[784494742817713399, "tower_service", false, 6110434461788295973], [1629840150976456681, "iri_string", false, 5707470129735101640], [1906322745568073236, "pin_project_lite", false, 13639927188980127958], [2620434475832828286, "http", false, 18232885740384947572], [5695049318159433696, "tower", false, 10585068319700653200], [6355489020061627772, "bytes", false, 10891763691645010383], [7712452662827335977, "tower_layer", false, 6887405756096252961], [9001817693037665195, "bitflags", false, 737841005004786991], [10629569228670356391, "futures_util", false, 797287676767826371], [14084095096285906100, "http_body", false, 9026284559170192414]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tower-http-67a2fea22e9c079e\\dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}