#include "weapon_config.h"
#include "../accuracy_config/accuracy_config.h"
#include "../damage_config/damage_config.h"

namespace WeaponConfig {

void InitializeWeaponProfiles() {
    // Light Pistols
    g_WeaponProfiles[WEAPON_PISTOL_LIGHT].szWeaponName = "Light Pistol";
    g_WeaponProfiles[WEAPON_PISTOL_LIGHT].nWeaponClass = WEAPON_PISTOL_LIGHT;
    g_WeaponProfiles[WEAPON_PISTOL_LIGHT].vecWeaponIds = {WEAPON_HKP2000, WEAPON_USP_SILENCER, WEAPON_GLOCK, WEAPON_P250, WEAPON_CZ75A, WEAPON_FIVESEVEN, WEAPON_TEC9, WEAPON_ELITE};
    g_WeaponProfiles[WEAPON_PISTOL_LIGHT].behavior = {false, true, false, false};

    // Heavy Pistols
    g_WeaponProfiles[WEAPON_PISTOL_HEAVY].szWeaponName = "Heavy Pistol";
    g_WeaponProfiles[WEAPON_PISTOL_HEAVY].nWeaponClass = WEAPON_PISTOL_HEAVY;
    g_WeaponProfiles[WEAPON_PISTOL_HEAVY].vecWeaponIds = {WEAPON_DEAGLE, WEAPON_REVOLVER};
    g_WeaponProfiles[WEAPON_PISTOL_HEAVY].behavior = {false, true, true, false};

    // SMGs
    g_WeaponProfiles[WEAPON_SMG].szWeaponName = "SMG";
    g_WeaponProfiles[WEAPON_SMG].nWeaponClass = WEAPON_SMG;
    g_WeaponProfiles[WEAPON_SMG].vecWeaponIds = {WEAPON_MAC10, WEAPON_MP9, WEAPON_MP7, WEAPON_MP5SD, WEAPON_UMP45, WEAPON_P90, WEAPON_BIZON};
    g_WeaponProfiles[WEAPON_SMG].behavior = {false, true, false, false};

    // Rifles
    g_WeaponProfiles[WEAPON_RIFLE].szWeaponName = "Rifle";
    g_WeaponProfiles[WEAPON_RIFLE].nWeaponClass = WEAPON_RIFLE;
    g_WeaponProfiles[WEAPON_RIFLE].vecWeaponIds = {WEAPON_AK47, WEAPON_M4A1, WEAPON_M4A1_SILENCER, WEAPON_GALILAR, WEAPON_FAMAS, WEAPON_AUG, WEAPON_SG556};
    g_WeaponProfiles[WEAPON_RIFLE].behavior = {false, true, false, false};

    // Shotguns
    g_WeaponProfiles[WEAPON_SHOTGUN].szWeaponName = "Shotgun";
    g_WeaponProfiles[WEAPON_SHOTGUN].nWeaponClass = WEAPON_SHOTGUN;
    g_WeaponProfiles[WEAPON_SHOTGUN].vecWeaponIds = {WEAPON_NOVA, WEAPON_XM1014, WEAPON_SAWEDOFF, WEAPON_MAG7};
    g_WeaponProfiles[WEAPON_SHOTGUN].behavior = {false, true, false, false};

    // Scout
    g_WeaponProfiles[WEAPON_SNIPER_SCOUT].szWeaponName = "Scout";
    g_WeaponProfiles[WEAPON_SNIPER_SCOUT].nWeaponClass = WEAPON_SNIPER_SCOUT;
    g_WeaponProfiles[WEAPON_SNIPER_SCOUT].vecWeaponIds = {WEAPON_SSG08};
    g_WeaponProfiles[WEAPON_SNIPER_SCOUT].behavior = {true, true, false, false};

    // Auto Sniper
    g_WeaponProfiles[WEAPON_SNIPER_AUTO].szWeaponName = "Auto Sniper";
    g_WeaponProfiles[WEAPON_SNIPER_AUTO].nWeaponClass = WEAPON_SNIPER_AUTO;
    g_WeaponProfiles[WEAPON_SNIPER_AUTO].vecWeaponIds = {WEAPON_SCAR20, WEAPON_G3SG1};
    g_WeaponProfiles[WEAPON_SNIPER_AUTO].behavior = {true, true, false, false};

    // AWP
    g_WeaponProfiles[WEAPON_SNIPER_BOLT].szWeaponName = "AWP";
    g_WeaponProfiles[WEAPON_SNIPER_BOLT].nWeaponClass = WEAPON_SNIPER_BOLT;
    g_WeaponProfiles[WEAPON_SNIPER_BOLT].vecWeaponIds = {WEAPON_AWP};
    g_WeaponProfiles[WEAPON_SNIPER_BOLT].behavior = {true, true, false, false};

    // LMGs
    g_WeaponProfiles[WEAPON_LMG].szWeaponName = "LMG";
    g_WeaponProfiles[WEAPON_LMG].nWeaponClass = WEAPON_LMG;
    g_WeaponProfiles[WEAPON_LMG].vecWeaponIds = {WEAPON_M249, WEAPON_NEGEV};
    g_WeaponProfiles[WEAPON_LMG].behavior = {false, true, false, false};

    // Other
    g_WeaponProfiles[WEAPON_OTHER].szWeaponName = "Other";
    g_WeaponProfiles[WEAPON_OTHER].nWeaponClass = WEAPON_OTHER;
    g_WeaponProfiles[WEAPON_OTHER].vecWeaponIds = {};
    g_WeaponProfiles[WEAPON_OTHER].behavior = {false, false, false, true};
}

EWeaponClass GetWeaponClass(int nWeaponId) {
    EMenuWeaponType menuType = GetWeaponMenuType(static_cast<EItemDefinitionIndexes>(nWeaponId));
    switch (menuType) {
        case EMenuWeaponType::LIGHT_PISTOL: return WEAPON_PISTOL_LIGHT;
        case EMenuWeaponType::DEAGLE:       return WEAPON_PISTOL_HEAVY;
        case EMenuWeaponType::REVOLVER:     return WEAPON_PISTOL_HEAVY;
        case EMenuWeaponType::SMG:          return WEAPON_SMG;
        case EMenuWeaponType::AR:           return WEAPON_RIFLE;
        case EMenuWeaponType::SHOTGUN:      return WEAPON_SHOTGUN;
        case EMenuWeaponType::SCOUT:        return WEAPON_SNIPER_SCOUT;
        case EMenuWeaponType::AUTOSNIPER:   return WEAPON_SNIPER_AUTO;
        case EMenuWeaponType::AWP:          return WEAPON_SNIPER_BOLT;
        case EMenuWeaponType::LMG:          return WEAPON_LMG;
        default:                            return WEAPON_OTHER;
    }
}

WeaponProfile_t& GetCurrentWeaponProfile() {
    if (!LocalPlayerData::m_pWeapon)
        return g_WeaponProfiles[WEAPON_OTHER];
    return g_WeaponProfiles[g_iCurrentWeaponClass];
}

WeaponProfile_t& GetWeaponProfile(EWeaponClass nClass) {
    if (nClass < 0 || nClass >= WEAPON_CLASS_MAX)
        return g_WeaponProfiles[WEAPON_OTHER];
    return g_WeaponProfiles[nClass];
}

bool HasWeaponChanged() {
    if (!LocalPlayerData::m_pWeapon)
        return false;
    int nCurrentWeaponId = LocalPlayerData::m_nWeaponDefinitionIndex;
    if (nCurrentWeaponId != g_iLastWeaponId) {
        g_iLastWeaponId = nCurrentWeaponId;
        return true;
    }
    return false;
}

void UpdateCurrentWeapon() {
    if (!LocalPlayerData::m_pWeapon)
        return;
    if (!HasWeaponChanged())
        return;
    
    EWeaponClass nNewClass = GetWeaponClass(LocalPlayerData::m_nWeaponDefinitionIndex);
    g_iCurrentWeaponClass = nNewClass;
    
    // Notify accuracy and damage configs of weapon change
    AccuracyConfig::UpdateWeaponClass(nNewClass);
    DamageConfig::UpdateWeaponClass(nNewClass);
}

const char* GetCurrentWeaponName() {
    if (!LocalPlayerData::m_pWeapon)
        return "No Weapon";
    return GetCurrentWeaponProfile().szWeaponName;
}

const char* GetWeaponClassName(EWeaponClass nClass) {
    switch (nClass) {
        case WEAPON_PISTOL_LIGHT: return "Light Pistol";
        case WEAPON_PISTOL_HEAVY: return "Heavy Pistol";
        case WEAPON_SMG:          return "SMG";
        case WEAPON_RIFLE:        return "Rifle";
        case WEAPON_SHOTGUN:      return "Shotgun";
        case WEAPON_SNIPER_SCOUT: return "Scout";
        case WEAPON_SNIPER_AUTO:  return "Auto Sniper";
        case WEAPON_SNIPER_BOLT:  return "AWP";
        case WEAPON_LMG:          return "LMG";
        case WEAPON_OTHER:        return "Other";
        default:                  return "Unknown";
    }
}

WeaponBehavior_t& GetCurrentBehavior() {
    return GetCurrentWeaponProfile().behavior;
}

WeaponBehavior_t& GetBehavior(EWeaponClass nClass) {
    return GetWeaponProfile(nClass).behavior;
}

void SetBehavior(EWeaponClass nClass, const WeaponBehavior_t& behavior) {
    if (nClass >= 0 && nClass < WEAPON_CLASS_MAX)
        g_WeaponProfiles[nClass].behavior = behavior;
}

void Initialize() {
    InitializeWeaponProfiles();
    g_iCurrentWeaponClass = WEAPON_OTHER;
    g_iLastWeaponId = -1;
    
    // Initialize separated config systems
    AccuracyConfig::Initialize();
    DamageConfig::Initialize();
}

} // namespace WeaponConfig