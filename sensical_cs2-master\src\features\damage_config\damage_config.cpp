#include "damage_config.h"

namespace DamageConfig {

void InitializeDamageProfiles() {
    // Light Pistol (class 0)
    g_DamageProfiles[0] = {20, 30, 0, {{true, HB_HEAD}, {true, HB_CHEST}, {true, HB_STOMACH}, {true, HB_PELVIS}}};
    
    // Heavy Pistol (class 1)
    g_DamageProfiles[1] = {40, 50, 0, {{true, HB_HEAD}, {true, HB_CHEST}, {true, HB_STOMACH}}};
    
    // SMG (class 2)
    g_DamageProfiles[2] = {15, 25, 0, {{true, HB_HEAD}, {true, HB_CHEST}, {true, HB_STOMACH}, {true, HB_PELVIS}, {false, HB_ARMS}}};
    
    // Rifle (class 3)
    g_DamageProfiles[3] = {25, 35, 0, {{true, HB_HEAD}, {true, HB_CHEST}, {true, HB_STOMACH}, {true, HB_PELVIS}}};
    
    // Shotgun (class 4)
    g_DamageProfiles[4] = {30, 40, 0, {{true, HB_HEAD}, {true, HB_CHEST}, {true, HB_STOMACH}, {true, HB_PELVIS}, {false, HB_LEGS}}};
    
    // Scout (class 5)
    g_DamageProfiles[5] = {50, 60, 0, {{true, HB_HEAD}, {true, HB_CHEST}, {true, HB_STOMACH}}};
    
    // Auto Sniper (class 6)
    g_DamageProfiles[6] = {60, 70, 0, {{true, HB_HEAD}, {true, HB_CHEST}, {true, HB_STOMACH}}};
    
    // AWP (class 7)
    g_DamageProfiles[7] = {70, 80, 0, {{true, HB_HEAD}, {true, HB_CHEST}, {true, HB_STOMACH}, {true, HB_PELVIS}}};
    
    // LMG (class 8)
    g_DamageProfiles[8] = {20, 30, 0, {{true, HB_HEAD}, {true, HB_CHEST}, {true, HB_STOMACH}, {true, HB_PELVIS}, {false, HB_ARMS}, {false, HB_LEGS}}};
    
    // Other (class 9)
    g_DamageProfiles[9] = {10, 20, 0, {}};
    
    // Initialize global with rifle defaults
    g_GlobalDamage = {25, 35, 0, {{true, HB_HEAD}, {true, HB_CHEST}, {true, HB_STOMACH}, {true, HB_PELVIS}}};
}

DamageSettings_t& GetCurrentDamage() {
    if (!g_bUsePerWeaponDamage)
        return g_GlobalDamage;
    if (g_iCurrentWeaponClass < 0 || g_iCurrentWeaponClass >= 10)
        return g_DamageProfiles[9];
    return g_DamageProfiles[g_iCurrentWeaponClass];
}

DamageSettings_t& GetDamageForWeapon(int nWeaponClass) {
    if (nWeaponClass < 0 || nWeaponClass >= 10)
        return g_DamageProfiles[9];
    return g_DamageProfiles[nWeaponClass];
}

void SetGlobalDamage(const DamageSettings_t& config) {
    g_GlobalDamage = config;
}

void SetWeaponDamage(int nWeaponClass, const DamageSettings_t& config) {
    if (nWeaponClass >= 0 && nWeaponClass < 10)
        g_DamageProfiles[nWeaponClass] = config;
}

int GetMinDamage() {
    return GetCurrentDamage().nMinDamage;
}

int GetMinDamageOverride() {
    return GetCurrentDamage().nMinDamageOverride;
}

int GetTargetPriority() {
    return GetCurrentDamage().nTargetPriority;
}

std::vector<MenuRageHitbox_t>& GetHitboxes() {
    return GetCurrentDamage().vecHitboxes;
}

void SetMinDamage(int nWeaponClass, int value) {
    if (nWeaponClass >= 0 && nWeaponClass < 10)
        g_DamageProfiles[nWeaponClass].nMinDamage = value;
}

void SetMinDamageOverride(int nWeaponClass, int value) {
    if (nWeaponClass >= 0 && nWeaponClass < 10)
        g_DamageProfiles[nWeaponClass].nMinDamageOverride = value;
}

void SetTargetPriority(int nWeaponClass, int value) {
    if (nWeaponClass >= 0 && nWeaponClass < 10)
        g_DamageProfiles[nWeaponClass].nTargetPriority = value;
}

void SetHitboxes(int nWeaponClass, const std::vector<MenuRageHitbox_t>& hitboxes) {
    if (nWeaponClass >= 0 && nWeaponClass < 10)
        g_DamageProfiles[nWeaponClass].vecHitboxes = hitboxes;
}

void UpdateWeaponClass(int nWeaponClass) {
    g_iCurrentWeaponClass = nWeaponClass;
}

void Initialize() {
    InitializeDamageProfiles();
    g_iCurrentWeaponClass = 9;
}

} // namespace DamageConfig