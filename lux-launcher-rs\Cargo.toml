[package]
name = "lux-launcher"
version = "1.0.0"
edition = "2021"
authors = ["Lux"]
description = "LUX CS2 Launcher"

[features]
default = ["embed_dll"]
embed_dll = []

[dependencies]
eframe = { version = "0.29", features = ["default"] }
egui = "0.29"
egui_extras = { version = "0.29", features = ["image"] }
image = { version = "0.25", default-features = false, features = ["png"] }
reqwest = { version = "0.12", features = ["blocking", "json", "multipart", "rustls-tls"], default-features = false }
obfstr = "0.4"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
sha2 = "0.10"
hex = "0.4"
raw-cpuid = "11"
winreg = "0.52"
parking_lot = "0.12"
once_cell = "1.19"
anyhow = "1.0"
thiserror = "1.0"
base64 = "0.21"
rfd = "0.14"
flate2 = { version = "1", default-features = false, features = ["rust_backend"] }

[dependencies.windows]
version = "0.58"
features = [
    "Win32_Foundation",
    "Win32_System_Threading",
    "Win32_System_ProcessStatus",
    "Win32_System_Diagnostics_ToolHelp",
    "Win32_System_Diagnostics_Debug",
    "Win32_System_Memory",
    "Win32_System_LibraryLoader",
    "Win32_Security",
    "Win32_System_Registry",
    "Win32_UI_WindowsAndMessaging",
    "Win32_Storage_FileSystem",
    "Win32_System_SystemInformation",
    "Win32_System_Com",
    "Win32_UI_Shell",
]

[profile.release]
opt-level = "z"
lto = "fat"
codegen-units = 1
panic = "abort"
strip = "symbols"
debug = 0
incremental = false
overflow-checks = false

[build-dependencies]
winres = "0.1"
flate2 = { version = "1", default-features = false, features = ["rust_backend"] }
