#include "../../Precompiled.h"

// Helper function to get the current weapon config index based on equipped weapon
int GetLegitbotWeaponConfigIndex()
{
	if (!LocalPlayerData::m_pWeapon)
		return 0;

	EMenuWeaponType weaponType = GetWeaponMenuType(static_cast<EItemDefinitionIndexes>(LocalPlayerData::m_nWeaponDefinitionIndex));
	
	switch (weaponType)
	{
	case EMenuWeaponType::LIGHT_PISTOL: return 0;
	case EMenuWeaponType::DEAGLE: return 1;
	case EMenuWeaponType::REVOLVER: return 2;
	case EMenuWeaponType::SMG: return 3;
	case EMenuWeaponType::LMG: return 4;
	case EMenuWeaponType::AR: return 5;
	case EMenuWeaponType::SHOTGUN: return 6;
	case EMenuWeaponType::SCOUT: return 7;
	case EMenuWeaponType::AUTOSNIPER: return 8;
	case EMenuWeaponType::AWP: return 9;
	default: return 0;
	}
}

std::vector<EHitBoxes> CLegitbot::LegitbotHB_To_HB(ELegitbotHitboxes nLegitbotHB)
{
	std::vector<EHitBoxes> vecToReturn;

	switch (nLegitbotHB)
	{
	case LEGITHITBOX_HEAD:
		vecToReturn.push_back(EHitBoxes::HITBOX_HEAD);
		break;
	case LEGITHITBOX_CHEST:
		vecToReturn.push_back(EHitBoxes::HITBOX_CHEST);
		break;
	case LEGITHITBOX_STOMACH:
		vecToReturn.push_back(EHitBoxes::HITBOX_STOMACH);
		break;
	case LEGITHITBOX_PELVIS:
		vecToReturn.push_back(EHitBoxes::HITBOX_PELVIS);
		break;
	case LEGITHITBOX_ARMS:
		vecToReturn.push_back(EHitBoxes::HITBOX_LEFT_FOREARM);
		vecToReturn.push_back(EHitBoxes::HITBOX_RIGHT_FOREARM);
		vecToReturn.push_back(EHitBoxes::HITBOX_LEFT_UPPER_ARM);
		vecToReturn.push_back(EHitBoxes::HITBOX_RIGHT_UPPER_ARM);
		break;
	case LEGITHITBOX_LEGS:
		vecToReturn.push_back(EHitBoxes::HITBOX_LEFT_CALF);
		vecToReturn.push_back(EHitBoxes::HITBOX_RIGHT_CALF);
		vecToReturn.push_back(EHitBoxes::HITBOX_LEFT_THIGH);
		vecToReturn.push_back(EHitBoxes::HITBOX_RIGHT_THIGH);
		break;
	case LEGITHITBOX_FEET:
		vecToReturn.push_back(EHitBoxes::HITBOX_LEFT_FOOT);
		vecToReturn.push_back(EHitBoxes::HITBOX_RIGHT_FOOT);
		break;
	default:
		break;
	}

	return vecToReturn;
}

QAngle CLegitbot::ApplySmoothing(QAngle aCurrentAngle, QAngle aTargetAngle, int iSmoothness, int iSmoothType, int iRandomization)
{
	QAngle aDelta = aTargetAngle - aCurrentAngle;
	aDelta.Normalize();

	float flRandom = 1.0f;
	if (iRandomization > 0) {
		float flRandomFactor = static_cast<float>(iRandomization) / 100.0f;
		flRandom = 1.0f + ((static_cast<float>(rand() % 100) / 100.0f - 0.5f) * flRandomFactor);
	}

	float flSmoothFactor = static_cast<float>(iSmoothness);
	
	switch (iSmoothType)
	{
	case 0: // Linear
		return aCurrentAngle + (aDelta / flSmoothFactor) * flRandom;
		
	case 1: // Ease-out (starts fast, slows down)
	{
		float flDistance = aDelta.Length();
		float flEase = 1.0f - powf(1.0f - (1.0f / flSmoothFactor), 2.0f);
		return aCurrentAngle + (aDelta * flEase) * flRandom;
	}
	
	case 2: // Ease-in-out (slow start and end)
	{
		float flT = 1.0f / flSmoothFactor;
		float flEase = flT < 0.5f ? 2.0f * flT * flT : 1.0f - powf(-2.0f * flT + 2.0f, 2.0f) / 2.0f;
		return aCurrentAngle + (aDelta * flEase) * flRandom;
	}
	
	default:
		return aCurrentAngle + (aDelta / flSmoothFactor) * flRandom;
	}
}

void CLegitbot::CreateMove()
{
	if (!Config::b(g_Variables.m_Legitbot.m_bEnabled))
		return;

	// Auto-weapon detection - uses unified system
	AutoWeaponConfig();

	if (!Input::HandleInput(Config::kb(g_Variables.m_Legitbot.m_iAimKey)))
		return;

	if (LocalPlayerData::m_pWeapon->m_iClip1() <= 0)
		return;

	if (LocalPlayerData::m_pWeapon->m_nNextPrimaryAttackTick() > Interfaces::m_pGlobalVariables->m_nTickCount)
		return;

	if (Config::vb(g_Variables.m_Legitbot.m_vbConditions).at(ELegitbotConditions::CONDITION_NO_FLASH) &&
		Globals::m_pLocalPlayerPawn->m_flFlashAlpha() > 0.f
		)
		return;

	// Use unified weapon config system
	WeaponConfig::WeaponProfile_t& profile = WeaponConfig::GetCurrentWeaponProfile();
	float flFov = static_cast<float>(profile.nLegitbotFov) / 4.f;
	int iSmoothness = profile.nLegitbotSmoothness;
	int iSmoothType = profile.nLegitbotSmoothType;
	int iRandomization = profile.nLegitbotRandomization;
	int iTargetPriority = profile.nLegitbotTargetPriority;
	const bool bAimPitch = Config::b(g_Variables.m_Legitbot.m_bAimPitch);
	const QAngle aViewAngles = Interfaces::m_pInput->GetViewAngles();

	LegitbotPlayer_t objBestPlayer = { nullptr, nullptr, { 0, 0, 0 }, 0, INT_MAX, FLT_MAX };

	for (auto& objPlayer : this->GetPlayers())
	{
		CModel* pModel = objPlayer.m_pPawn->m_pGameSceneNode()->GetSkeletonInstance()->m_modelState().m_modelHandle;
		if (!pModel)
			continue;

		std::vector<CHitBox*> vecHitboxes;

		for (int i = 0; i < ELegitbotHitboxes::LEGITHITBOX_MAX; i++)
		{
			if (!Config::vb(g_Variables.m_Legitbot.m_vbHitboxes).at(i))
				continue;

			auto vecSelectedHitboxes = LegitbotHB_To_HB((ELegitbotHitboxes)i);
			for (auto& nHitboxIndex : vecSelectedHitboxes)
			{
				vecHitboxes.push_back(pModel->GetHitBox(nHitboxIndex));
			}
		}

		GameTrace_t trace = GameTrace_t();
		TraceFilter_t filter = TraceFilter_t(0x1C3003, Globals::m_pLocalPlayerPawn, nullptr, 4);
		Ray_t ray = Ray_t();

		QAngle aBestHitboxAngle = { };
		Vector vBestHitboxPosition = { };
		float flBestHitboxDistance = FLT_MAX;

		for (auto& pHitbox : vecHitboxes)
		{
			Vector vHitboxPos = objPlayer.m_pPawn->m_pGameSceneNode()->GetBonePosition(HitboxToBoneIndex(pHitbox->m_nHitboxIndex));
			QAngle aHitboxAngle = Math::CalcAngle(Globals::m_pLocalPlayerPawn->GetEyePosition(true), vHitboxPos);
			if (!bAimPitch)
				aHitboxAngle.x = aViewAngles.x;
			float flDistance = Math::GetFOV(aViewAngles, aHitboxAngle);
			if (flDistance < flBestHitboxDistance)
			{
				flBestHitboxDistance = flDistance;
				vBestHitboxPosition = vHitboxPos;
				aBestHitboxAngle = aHitboxAngle;
			}
		}

		if (vBestHitboxPosition.IsZero() || aBestHitboxAngle.IsZero() || vBestHitboxPosition.IsZero())
			continue;

		Interfaces::m_pVPhys2World->TraceShape(&ray, Globals::m_pLocalPlayerPawn->GetEyePosition(), vBestHitboxPosition, &filter, &trace);
		if (trace.m_pHitEntity != objPlayer.m_pPawn)
			continue;

		if (Config::vb(g_Variables.m_Legitbot.m_vbConditions).at(ELegitbotConditions::CONDITION_VISIBLE) &&
			!trace.IsVisible()
			)
			continue;

		if (Config::vb(g_Variables.m_Legitbot.m_vbConditions).at(ELegitbotConditions::CONDITION_NO_SMOKE) &&
			!Utilities::LineGoesThroughSmoke(Globals::m_pLocalPlayerPawn->GetEyePosition(), vBestHitboxPosition, 0.9f))
			continue;

		if (flBestHitboxDistance > flFov)
			continue;
	
		objPlayer.m_iFov_CenterToAimpos = flBestHitboxDistance;
		objPlayer.m_aShootAngle = aBestHitboxAngle;
		objPlayer.m_flDistance = (objPlayer.m_pPawn->m_pGameSceneNode()->m_vecAbsOrigin() - Globals::m_pLocalPlayerPawn->m_pGameSceneNode()->m_vecAbsOrigin()).Length();

		bool bIsBetter = false;
		switch (iTargetPriority)
		{
		case 0: // Closest to crosshair
			bIsBetter = flBestHitboxDistance < objBestPlayer.m_iFov_CenterToAimpos;
			break;
		case 1: // Lowest health
			bIsBetter = objPlayer.m_iHealth < objBestPlayer.m_iHealth || 
				(objPlayer.m_iHealth == objBestPlayer.m_iHealth && flBestHitboxDistance < objBestPlayer.m_iFov_CenterToAimpos);
			break;
		case 2: // Closest distance
			bIsBetter = objPlayer.m_flDistance < objBestPlayer.m_flDistance ||
				(objPlayer.m_flDistance == objBestPlayer.m_flDistance && flBestHitboxDistance < objBestPlayer.m_iFov_CenterToAimpos);
			break;
		default:
			bIsBetter = flBestHitboxDistance < objBestPlayer.m_iFov_CenterToAimpos;
			break;
		}

		if (bIsBetter)
			objBestPlayer = objPlayer;
	}

	if (!objBestPlayer.m_pController || 
		!objBestPlayer.m_pPawn ||
		objBestPlayer.m_aShootAngle.IsZero())
		return;

	QAngle aCurrentAngle = aViewAngles;
	QAngle aSmoothedAngle = ApplySmoothing(aCurrentAngle, objBestPlayer.m_aShootAngle, iSmoothness, iSmoothType, iRandomization);

	Interfaces::m_pInput->SetViewAngle(aSmoothedAngle);
}

std::vector<CLegitbot::LegitbotPlayer_t> CLegitbot::GetPlayers()
{
	std::vector<CLegitbot::LegitbotPlayer_t> vecOut;

	for (auto& objEntity : g_Entities->m_vecPlayersOnly)
	{
		CCSPlayerController* pController = (CCSPlayerController*)objEntity.m_pEntity;
		if (!pController)
			continue;

		C_CSPlayerPawn* pPawn = pController->m_hPlayerPawn().Get();
		if (!pPawn)
			continue;

		if (!pController->m_bPawnIsAlive())
			continue;

		if (!Globals::m_pLocalPlayerPawn->IsEnemy(pPawn))
			continue;

		LegitbotPlayer_t objLegitbotPlayer = { };

		objLegitbotPlayer.m_pController = pController;
		objLegitbotPlayer.m_pPawn = pPawn;
		objLegitbotPlayer.m_iHealth = objLegitbotPlayer.m_pPawn->m_iHealth();
		objLegitbotPlayer.m_flDistance = FLT_MAX;

		vecOut.emplace_back(objLegitbotPlayer);
	}

	return vecOut;
}

void CLegitbot::DrawFovCircle()
{
	if (!Config::b(g_Variables.m_Legitbot.m_bEnabled))
		return;

	if (!Config::b(g_Variables.m_Legitbot.m_bDrawFovCircle))
		return;

	if (!Interfaces::m_pEngine->IsInGame() || !Interfaces::m_pEngine->IsConnected())
		return;

	if (!Globals::m_pLocalPlayerPawn || !Globals::m_pLocalPlayerController)
		return;

	int iWeaponConfig = GetLegitbotWeaponConfigIndex();
	float flFov = static_cast<float>(Config::vi(g_Variables.m_Legitbot.m_viFov)[iWeaponConfig]);
	
	ImVec2 screenCenter = ImVec2(ImGui::GetIO().DisplaySize.x * 0.5f, ImGui::GetIO().DisplaySize.y * 0.5f);
	
	float flRadius = tanf(flFov * (M_PI / 180.0f) * 0.5f) * (ImGui::GetIO().DisplaySize.x / tanf(90.0f * (M_PI / 180.0f) * 0.5f));
	
	Color col = Config::c(g_Variables.m_Legitbot.m_colFovCircle);
	ImU32 color = IM_COL32(col.r(), col.g(), col.b(), col.a());
	
	ImGui::GetBackgroundDrawList()->AddCircle(screenCenter, flRadius, color, 64, 1.0f);
}

void CLegitbot::RCS()
{
	if (!Config::b(g_Variables.m_Legitbot.m_bRCS))
		return;

	// Prevent conflict with Ragebot
	if (Config::b(g_Variables.m_Ragebot.m_bRagebotEnabled))
		return;

	if (!Interfaces::m_pEngine->IsInGame() || !Interfaces::m_pEngine->IsConnected())
		return;

	if (!Globals::m_pLocalPlayerPawn || !Globals::m_pLocalPlayerController)
		return;

	if (!LocalPlayerData::m_pWeapon || !Globals::m_pCmd)
		return;

	int iWeaponConfig = GetLegitbotWeaponConfigIndex();
	int iRCSX = Config::vi(g_Variables.m_Legitbot.m_viRCSX)[iWeaponConfig];
	int iRCSY = Config::vi(g_Variables.m_Legitbot.m_viRCSY)[iWeaponConfig];
	int iRCSStart = Config::i(g_Variables.m_Legitbot.m_iRCSStart);

	if (iRCSX == 0 && iRCSY == 0)
		return;

	// Get punch from cache (CS2 uses m_aimPunchCache, not m_aimPunchAngle)
	auto& aimPunchCache = Globals::m_pLocalPlayerPawn->m_aimPunchCache();
	if (aimPunchCache.m_nSize <= 0 || aimPunchCache.m_nSize <= iRCSStart) {
		m_aLastPunch = { 0, 0, 0 };
		return;
	}

	QAngle aPunch = aimPunchCache.At(aimPunchCache.m_nSize - 1);

	// Calculate delta (difference from last frame's punch)
	QAngle aDelta = {
		(aPunch.x - m_aLastPunch.x) * (static_cast<float>(iRCSY) / 100.0f) * 2.0f,
		(aPunch.y - m_aLastPunch.y) * (static_cast<float>(iRCSX) / 100.0f) * 2.0f,
		0.0f
	};

	if (Config::b(g_Variables.m_Legitbot.m_bSilentRCS)) {
		// Silent RCS: Read and modify angles directly from command (dynamic)
		auto pBaseCmd = Globals::m_pCmd->m_csgoUserCmd.mutable_base();
		if (pBaseCmd && pBaseCmd->has_viewangles()) {
			QAngle aCmdAngle(
				pBaseCmd->viewangles().x() - aDelta.x,
				pBaseCmd->viewangles().y() - aDelta.y,
				0.0f
			);aCmdAngle.Normalize();

			pBaseCmd->mutable_viewangles()->set_x(aCmdAngle.x);
			pBaseCmd->mutable_viewangles()->set_y(aCmdAngle.y);
		}

		// Apply to input history - read each entry's angles dynamically
		for (int i = 0; i < Globals::m_pCmd->m_csgoUserCmd.input_history_size(); i++) {
			CSGOInputHistoryEntryPB* pInputEntry = Globals::m_pCmd->m_csgoUserCmd.mutable_input_history(i);
			if (!pInputEntry || !pInputEntry->has_view_angles())
				continue;

			QAngle aHistAngle(
				pInputEntry->view_angles().x() - aDelta.x,
				pInputEntry->view_angles().y() - aDelta.y,
				0.0f
			);
			aHistAngle.Normalize();

			pInputEntry->mutable_view_angles()->set_x(aHistAngle.x);
			pInputEntry->mutable_view_angles()->set_y(aHistAngle.y);
		}
	}
	else {
		// Normal RCS: Move visual crosshair
		QAngle aCurrentAngle = Interfaces::m_pInput->GetViewAngles();
		QAngle aNewAngle = aCurrentAngle - aDelta;
		aNewAngle.Normalize();

		Interfaces::m_pInput->SetViewAngle(aNewAngle);
	}

	m_aLastPunch = aPunch;
}

// Unified auto-weapon detection for legitbot
void CLegitbot::AutoWeaponConfig() {
	// Use unified weapon config system - single source of truth
	WeaponConfig::AutoWeaponUpdate();
}
