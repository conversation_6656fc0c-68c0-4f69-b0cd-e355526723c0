{"rustc": 17273738520181233363, "features": "[\"accesskit_unix\", \"async-io\", \"default\", \"rwh_06\"]", "declared_features": "[\"accesskit_unix\", \"async-io\", \"default\", \"rwh_05\", \"rwh_06\", \"tokio\"]", "target": 14667884907678119804, "profile": 10004052855522644898, "path": 6652009371464246169, "deps": [[378321411956646689, "accesskit", false, 11602921479520497865], [4143744114649553716, "rwh_06", false, 13756357425997535205], [6734646761890249140, "accesskit_windows", false, 11163964038005624612], [9442355480334427104, "winit", false, 7056866601603776005]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\accesskit_winit-592269be0de0e844\\dep-lib-accesskit_winit", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}