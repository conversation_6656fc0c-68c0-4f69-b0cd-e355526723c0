{"rustc": 17273738520181233363, "features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"json\", \"multipart\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"system-proxy\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 447021486742529345, "path": 12447214367351147576, "deps": [[64645024058175247, "rustls_pki_types", false, 13701789718856390920], [390686634370472506, "webpki_roots", false, 6700712543344799491], [784494742817713399, "tower_service", false, 5423646757356596458], [1788832197870803419, "hyper_rustls", false, 14372409478121696169], [1811549171721445101, "futures_channel", false, 15265428364477085481], [1906322745568073236, "pin_project_lite", false, 3353463425990293210], [1991942485830005045, "tokio_rustls", false, 1508388770688701334], [2517136641825875337, "sync_wrapper", false, 6971976851921099655], [2620434475832828286, "http", false, 7847288227143676186], [4160778395972110362, "hyper", false, 13082237341482121111], [5296164962160813001, "rustls", false, 260744791136367201], [5404511084185685755, "url", false, 3663568773976190614], [5695049318159433696, "tower", false, 11567243148772245461], [6355489020061627772, "bytes", false, 1224965927141285281], [6803352382179706244, "percent_encoding", false, 7658537741579607242], [7620660491849607393, "futures_core", false, 13896407726154415918], [7720834239451334583, "tokio", false, 17316964368110174770], [8098305783429564872, "hyper_util", false, 10988586421939098882], [8434721349366383850, "tower_http", false, 12991564343727222830], [10629569228670356391, "futures_util", false, 10931623315811504528], [10630857666389190470, "log", false, 17461750151385638743], [12832915883349295919, "serde_json", false, 291123396021626230], [13077212702700853852, "base64", false, 708526676009854776], [13548984313718623784, "serde", false, 5451951047850018032], [14084095096285906100, "http_body", false, 5351138267543998722], [16542808166767769916, "serde_urlencoded", false, 6059718344655169011], [16900715236047033623, "http_body_util", false, 4805314798203677377], [18071510856783138481, "mime_guess", false, 2844007207628176919]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-fef33741f4545b8f\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}